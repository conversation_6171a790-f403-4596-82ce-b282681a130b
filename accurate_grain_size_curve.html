<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Particle Size Distribution Curve - Accurate Data</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: linear-gradient(145deg, rgba(255,255,255,0.95) 0%, rgba(248,249,250,0.9) 100%);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid transparent;
            border-image: linear-gradient(90deg, #667eea, #764ba2, #f093fb) 1;
        }
        h1 {
            font-size: 28px;
            font-weight: bold;
            background: linear-gradient(45deg, #667eea, #764ba2, #f093fb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
            font-family: Arial, sans-serif;
        }
        .subtitle {
            font-size: 16px;
            color: #7f8c8d;
            font-style: italic;
            font-family: Arial, sans-serif;
        }
        .chart-container {
            width: 100%;
            height: 700px;
            margin: 30px 0;
            background: linear-gradient(135deg, 
                rgba(102, 126, 234, 0.05) 0%, 
                rgba(118, 75, 162, 0.05) 25%,
                rgba(240, 147, 251, 0.05) 50%,
                rgba(245, 87, 108, 0.05) 75%,
                rgba(79, 172, 254, 0.05) 100%);
            border-radius: 15px;
            padding: 20px;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.05);
            border: 1px solid rgba(102, 126, 234, 0.1);
        }
        .data-section {
            margin-top: 40px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        .statistics {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        .statistics h3 {
            color: white;
            margin-top: 0;
            font-size: 18px;
            font-family: Arial, sans-serif;
        }
        .classification {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3);
        }
        .classification h3 {
            color: white;
            margin-top: 0;
            font-size: 18px;
            font-family: Arial, sans-serif;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
            margin-top: 20px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            font-family: Arial, sans-serif;
        }
        .data-table th, .data-table td {
            border: 1px solid #e0e0e0;
            padding: 12px;
            text-align: center;
        }
        .data-table th {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            font-weight: bold;
        }
        .data-table td {
            background: rgba(255,255,255,0.8);
        }
        .data-table tr:nth-child(even) td {
            background: rgba(248,249,250,0.8);
        }
        .stat-item, .class-item {
            margin: 10px 0;
            font-size: 14px;
            font-family: Arial, sans-serif;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid transparent;
            border-image: linear-gradient(90deg, #667eea, #f093fb) 1;
            font-size: 12px;
            color: #7f8c8d;
            text-align: center;
            font-family: Arial, sans-serif;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Particle Size Distribution Curve</h1>
            <div class="subtitle">Silty Sand Sample - Geotechnical Analysis</div>
        </div>
        
        <div id="chart" class="chart-container"></div>
        
        <div class="data-section">
            <div>
                <div class="statistics">
                    <h3>Statistical Parameters</h3>
                    <div class="stat-item"><strong>Sample Count:</strong> 4 tests</div>
                    <div class="stat-item"><strong>Soil Type:</strong> Silty Sand</div>
                    <div class="stat-item"><strong>Gravel (>2mm):</strong> 59.1% (avg)</div>
                    <div class="stat-item"><strong>Sand (0.075-2mm):</strong> 19.3% (avg)</div>
                    <div class="stat-item"><strong>Fines (<0.075mm):</strong> 21.6% (avg)</div>
                </div>
                
                <div class="classification">
                    <h3>Classification Results</h3>
                    <div class="class-item"><strong>USCS:</strong> SM (Silty Sand)</div>
                    <div class="class-item"><strong>Uniformity Coeff:</strong> Well-graded</div>
                    <div class="class-item"><strong>Plasticity:</strong> Non-plastic</div>
                    <div class="class-item"><strong>Permeability:</strong> Moderate</div>
                </div>
            </div>
            
            <div>
                <h3 style="color: #2c3e50; margin-bottom: 15px; font-family: Arial, sans-serif;">Original Data Table</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th rowspan="2">Soil Layer</th>
                            <th rowspan="2">Statistical Index</th>
                            <th colspan="6">Particle Size Composition (%)</th>
                        </tr>
                        <tr>
                            <th>&gt;20.0</th>
                            <th>20.0~2.00</th>
                            <th>2.00~0.50</th>
                            <th>0.50~0.25</th>
                            <th>0.25~0.075</th>
                            <th>&lt;0.075</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td rowspan="4">Silty Sand</td>
                            <td>Average</td>
                            <td>22.8</td>
                            <td>36.3</td>
                            <td>14.5</td>
                            <td>4.8</td>
                            <td>21.6</td>
                            <td>-</td>
                        </tr>
                        <tr>
                            <td>Count</td>
                            <td>4</td>
                            <td>4</td>
                            <td>4</td>
                            <td>4</td>
                            <td>4</td>
                            <td>-</td>
                        </tr>
                        <tr>
                            <td>Maximum</td>
                            <td>38.5</td>
                            <td>43.2</td>
                            <td>17.3</td>
                            <td>6.3</td>
                            <td>31.1</td>
                            <td>-</td>
                        </tr>
                        <tr>
                            <td>Minimum</td>
                            <td>12.0</td>
                            <td>29.3</td>
                            <td>22.2</td>
                            <td>14.3</td>
                            <td>21.3</td>
                            <td>-</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="footer">
            Generated with Accurate Data | Standards: ASTM D422, ASTM D6913 | Font: Arial
        </div>
    </div>

    <script>
        // Accurate data processing based on the provided table
        // Particle sizes (sieve openings in mm)
        const particleSizes = [100, 20.0, 2.0, 0.5, 0.25, 0.075, 0.01];
        
        // Calculate cumulative passing percentages from retained percentages
        // Starting from 100% and subtracting each retained fraction
        const cumulativeMean = [100, 77.2, 40.9, 26.4, 21.6, 0, 0];
        const cumulativeMax = [100, 61.5, 18.3, 1.0, 0, 0, 0];  // Based on minimum retained values
        const cumulativeMin = [100, 87.5, 44.3, 27.0, 20.7, 0, 0];  // Based on maximum retained values
        
        // Main curve with rich colors and data labels
        const mainTrace = {
            x: particleSizes,
            y: cumulativeMean,
            mode: 'lines+markers+text',
            name: 'Average Curve',
            line: {
                color: '#667eea',
                width: 5,
                shape: 'spline'
            },
            marker: {
                size: [14, 12, 14, 12, 14, 14, 10],
                color: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff'],
                line: {
                    color: '#667eea',
                    width: 3
                },
                symbol: ['circle', 'square', 'diamond', 'triangle-up', 'star', 'hexagon', 'cross']
            },
            text: ['100%', '77.2%', '40.9%', '26.4%', '21.6%', '0%', '0%'],
            textposition: 'top center',
            textfont: {
                size: 12,
                color: '#667eea',
                family: 'Arial',
                style: 'bold'
            },
            hovertemplate: '<b>Particle Size:</b> %{x} mm<br><b>Passing:</b> %{y}%<extra></extra>'
        };
        
        // Data range envelope
        const envelopeTrace = {
            x: particleSizes.concat(particleSizes.slice().reverse()),
            y: cumulativeMax.concat(cumulativeMin.slice().reverse()),
            fill: 'toself',
            fillcolor: 'rgba(102, 126, 234, 0.15)',
            line: {color: 'transparent'},
            name: 'Data Range',
            showlegend: true
        };
        
        // Maximum curve
        const maxTrace = {
            x: particleSizes,
            y: cumulativeMax,
            mode: 'lines+markers',
            name: 'Maximum',
            line: {
                color: '#f093fb',
                width: 3,
                dash: 'dash',
                shape: 'spline'
            },
            marker: {
                size: 8,
                color: '#f093fb',
                symbol: 'triangle-up'
            }
        };
        
        // Minimum curve
        const minTrace = {
            x: particleSizes,
            y: cumulativeMin,
            mode: 'lines+markers',
            name: 'Minimum',
            line: {
                color: '#4facfe',
                width: 3,
                dash: 'dash',
                shape: 'spline'
            },
            marker: {
                size: 8,
                color: '#4facfe',
                symbol: 'triangle-down'
            }
        };
        
        const data = [envelopeTrace, mainTrace, maxTrace, minTrace];
        
        const layout = {
            title: {
                text: '',
                font: {
                    family: 'Arial',
                    size: 18,
                    color: '#2c3e50'
                }
            },
            xaxis: {
                title: {
                    text: 'Particle Size (mm)',
                    font: {
                        family: 'Arial',
                        size: 16,
                        color: '#2c3e50'
                    }
                },
                type: 'log',
                range: [Math.log10(0.01), Math.log10(100)],
                tickvals: [0.01, 0.075, 0.25, 0.5, 2.0, 20.0, 100],
                ticktext: ['0.01', '0.075', '0.25', '0.5', '2.0', '20.0', '100'],
                gridcolor: 'rgba(102, 126, 234, 0.3)',
                gridwidth: 2,
                tickfont: {
                    family: 'Arial',
                    size: 12,
                    color: '#2c3e50'
                }
            },
            yaxis: {
                title: {
                    text: 'Percentage Passing (%)',
                    font: {
                        family: 'Arial',
                        size: 16,
                        color: '#2c3e50'
                    }
                },
                range: [0, 100],
                dtick: 10,
                gridcolor: 'rgba(240, 147, 251, 0.3)',
                gridwidth: 2,
                tickfont: {
                    family: 'Arial',
                    size: 12,
                    color: '#2c3e50'
                }
            },
            plot_bgcolor: 'rgba(255, 255, 255, 0.8)',
            paper_bgcolor: 'rgba(255, 255, 255, 0.9)',
            font: {
                family: 'Arial',
                size: 12,
                color: '#2c3e50'
            },
            legend: {
                x: 0.02,
                y: 0.98,
                bgcolor: 'rgba(255,255,255,0.9)',
                bordercolor: 'rgba(102, 126, 234, 0.5)',
                borderwidth: 2,
                font: {
                    family: 'Arial',
                    size: 12,
                    color: '#2c3e50'
                }
            },
            shapes: [
                // Soil classification boundaries with colors
                {
                    type: 'line',
                    x0: 0.075, x1: 0.075,
                    y0: 0, y1: 100,
                    line: {
                        color: '#f5576c',
                        width: 3,
                        dash: 'dot'
                    }
                },
                {
                    type: 'line',
                    x0: 2.0, x1: 2.0,
                    y0: 0, y1: 100,
                    line: {
                        color: '#f5576c',
                        width: 3,
                        dash: 'dot'
                    }
                },
                {
                    type: 'line',
                    x0: 20.0, x1: 20.0,
                    y0: 0, y1: 100,
                    line: {
                        color: '#f5576c',
                        width: 3,
                        dash: 'dot'
                    }
                }
            ],
            annotations: [
                {
                    x: Math.log10(0.03),
                    y: 10,
                    text: 'Fines<br>(<0.075mm)',
                    showarrow: false,
                    font: {
                        family: 'Arial',
                        size: 11,
                        color: 'white'
                    },
                    bgcolor: 'rgba(102, 126, 234, 0.9)',
                    bordercolor: 'rgba(67, 56, 202, 0.8)',
                    borderwidth: 2,
                    borderradius: 8
                },
                {
                    x: Math.log10(0.8),
                    y: 10,
                    text: 'Sand<br>(0.075-2mm)',
                    showarrow: false,
                    font: {
                        family: 'Arial',
                        size: 11,
                        color: 'white'
                    },
                    bgcolor: 'rgba(240, 147, 251, 0.9)',
                    bordercolor: 'rgba(245, 87, 108, 0.8)',
                    borderwidth: 2,
                    borderradius: 8
                },
                {
                    x: Math.log10(8),
                    y: 10,
                    text: 'Gravel<br>(2-20mm)',
                    showarrow: false,
                    font: {
                        family: 'Arial',
                        size: 11,
                        color: 'white'
                    },
                    bgcolor: 'rgba(245, 87, 108, 0.9)',
                    bordercolor: 'rgba(240, 147, 251, 0.8)',
                    borderwidth: 2,
                    borderradius: 8
                },
                {
                    x: Math.log10(50),
                    y: 10,
                    text: 'Cobbles<br>(>20mm)',
                    showarrow: false,
                    font: {
                        family: 'Arial',
                        size: 11,
                        color: 'white'
                    },
                    bgcolor: 'rgba(79, 172, 254, 0.9)',
                    bordercolor: 'rgba(102, 126, 234, 0.8)',
                    borderwidth: 2,
                    borderradius: 8
                }
            ]
        };
        
        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToAdd: ['downloadPlot'],
            toImageButtonOptions: {
                format: 'png',
                filename: 'accurate_particle_size_distribution',
                height: 700,
                width: 1200,
                scale: 2
            }
        };
        
        Plotly.newPlot('chart', data, layout, config);
    </script>
</body>
</html>
