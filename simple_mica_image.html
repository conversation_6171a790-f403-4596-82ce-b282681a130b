<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mica Image</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: transparent;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .mica-container {
            width: 800px;
            height: 600px;
            position: relative;
            background: transparent;
        }
        .mica-flake {
            position: absolute;
            background: linear-gradient(45deg, #FFD700 0%, #FFA500 25%, #FF8C00 50%, #FFD700 75%, #B8860B 100%);
            border-radius: 50% 20% 50% 20%;
            animation: shimmer 4s ease-in-out infinite;
            box-shadow: 
                0 0 20px rgba(255, 215, 0, 0.8),
                inset 0 0 10px rgba(255, 255, 255, 0.3);
            transform-origin: center;
            border: 1px solid rgba(184, 134, 11, 0.5);
        }
        .mica-flake::before {
            content: '';
            position: absolute;
            top: 10%;
            left: 10%;
            right: 10%;
            bottom: 10%;
            background: linear-gradient(135deg, rgba(255,255,255,0.4) 0%, transparent 50%, rgba(255,255,255,0.2) 100%);
            border-radius: 50% 20% 50% 20%;
        }
        .mica-flake::after {
            content: '';
            position: absolute;
            top: 20%;
            left: 20%;
            right: 20%;
            bottom: 20%;
            background: radial-gradient(ellipse at 30% 30%, rgba(255,255,255,0.6) 0%, transparent 70%);
            border-radius: 50% 20% 50% 20%;
        }
        @keyframes shimmer {
            0% { 
                opacity: 0.8; 
                transform: scale(1) rotate(0deg);
                filter: brightness(1);
            }
            25% { 
                opacity: 1; 
                transform: scale(1.02) rotate(1deg);
                filter: brightness(1.2);
            }
            50% { 
                opacity: 0.9; 
                transform: scale(1.05) rotate(-0.5deg);
                filter: brightness(1.4);
            }
            75% { 
                opacity: 1; 
                transform: scale(1.02) rotate(0.5deg);
                filter: brightness(1.1);
            }
            100% { 
                opacity: 0.8; 
                transform: scale(1) rotate(0deg);
                filter: brightness(1);
            }
        }
        /* Large central mica flake */
        .main-mica {
            width: 200px;
            height: 140px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            animation-delay: 0s;
            z-index: 10;
        }
        /* Medium mica flakes */
        .medium-mica-1 {
            width: 120px;
            height: 80px;
            top: 20%;
            left: 15%;
            animation-delay: 1s;
            z-index: 8;
        }
        .medium-mica-2 {
            width: 130px;
            height: 90px;
            top: 25%;
            right: 10%;
            animation-delay: 2s;
            z-index: 7;
        }
        .medium-mica-3 {
            width: 110px;
            height: 75px;
            bottom: 20%;
            left: 20%;
            animation-delay: 1.5s;
            z-index: 6;
        }
        .medium-mica-4 {
            width: 125px;
            height: 85px;
            bottom: 15%;
            right: 25%;
            animation-delay: 2.5s;
            z-index: 5;
        }
        /* Small mica flakes */
        .small-mica-1 {
            width: 70px;
            height: 50px;
            top: 10%;
            left: 45%;
            animation-delay: 0.5s;
            z-index: 4;
        }
        .small-mica-2 {
            width: 60px;
            height: 40px;
            top: 70%;
            left: 60%;
            animation-delay: 3s;
            z-index: 3;
        }
        .small-mica-3 {
            width: 65px;
            height: 45px;
            top: 60%;
            right: 5%;
            animation-delay: 1.8s;
            z-index: 2;
        }
        /* Tiny mica flakes */
        .tiny-mica-1 {
            width: 35px;
            height: 25px;
            top: 35%;
            left: 10%;
            animation-delay: 2.2s;
            z-index: 1;
        }
        .tiny-mica-2 {
            width: 30px;
            height: 20px;
            top: 80%;
            left: 40%;
            animation-delay: 3.5s;
            z-index: 1;
        }
        .tiny-mica-3 {
            width: 40px;
            height: 28px;
            bottom: 5%;
            right: 45%;
            animation-delay: 0.8s;
            z-index: 1;
        }
    </style>
</head>
<body>
    <div class="mica-container">
        <!-- Main large mica flake in center -->
        <div class="mica-flake main-mica"></div>
        
        <!-- Medium sized mica flakes -->
        <div class="mica-flake medium-mica-1"></div>
        <div class="mica-flake medium-mica-2"></div>
        <div class="mica-flake medium-mica-3"></div>
        <div class="mica-flake medium-mica-4"></div>
        
        <!-- Small mica flakes -->
        <div class="mica-flake small-mica-1"></div>
        <div class="mica-flake small-mica-2"></div>
        <div class="mica-flake small-mica-3"></div>
        
        <!-- Tiny mica flakes -->
        <div class="mica-flake tiny-mica-1"></div>
        <div class="mica-flake tiny-mica-2"></div>
        <div class="mica-flake tiny-mica-3"></div>
    </div>
</body>
</html>
