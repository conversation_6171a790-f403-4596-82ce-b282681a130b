<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mica Mineral Illustration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 25%, #2c3e50 50%, #34495e 75%, #2c3e50 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #f39c12;
        }
        h1 {
            font-size: 32px;
            font-weight: bold;
            background: linear-gradient(45deg, #f39c12, #e67e22, #d35400);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
            font-family: Arial, sans-serif;
        }
        .subtitle {
            font-size: 18px;
            color: #7f8c8d;
            font-style: italic;
            font-family: Arial, sans-serif;
        }
        .mica-showcase {
            width: 100%;
            height: 500px;
            background: radial-gradient(ellipse at center, #1a1a1a 0%, #000000 70%);
            border-radius: 15px;
            position: relative;
            overflow: hidden;
            margin: 30px 0;
            box-shadow: inset 0 0 50px rgba(0,0,0,0.5);
            border: 3px solid #f39c12;
        }
        .mica-flake {
            position: absolute;
            background: linear-gradient(45deg, #FFD700 0%, #FFA500 25%, #FF8C00 50%, #FFD700 75%, #B8860B 100%);
            border-radius: 50% 20% 50% 20%;
            animation: shimmer 4s ease-in-out infinite;
            box-shadow: 
                0 0 20px rgba(255, 215, 0, 0.8),
                inset 0 0 10px rgba(255, 255, 255, 0.3);
            transform-origin: center;
        }
        .mica-flake::before {
            content: '';
            position: absolute;
            top: 10%;
            left: 10%;
            right: 10%;
            bottom: 10%;
            background: linear-gradient(135deg, rgba(255,255,255,0.4) 0%, transparent 50%, rgba(255,255,255,0.2) 100%);
            border-radius: 50% 20% 50% 20%;
        }
        .mica-flake::after {
            content: '';
            position: absolute;
            top: 20%;
            left: 20%;
            right: 20%;
            bottom: 20%;
            background: radial-gradient(ellipse at 30% 30%, rgba(255,255,255,0.6) 0%, transparent 70%);
            border-radius: 50% 20% 50% 20%;
        }
        @keyframes shimmer {
            0% { 
                opacity: 0.7; 
                transform: scale(1) rotate(0deg);
                filter: brightness(1);
            }
            25% { 
                opacity: 1; 
                transform: scale(1.05) rotate(2deg);
                filter: brightness(1.3);
            }
            50% { 
                opacity: 0.9; 
                transform: scale(1.1) rotate(-1deg);
                filter: brightness(1.5);
            }
            75% { 
                opacity: 1; 
                transform: scale(1.05) rotate(1deg);
                filter: brightness(1.2);
            }
            100% { 
                opacity: 0.7; 
                transform: scale(1) rotate(0deg);
                filter: brightness(1);
            }
        }
        .large-mica {
            width: 120px;
            height: 80px;
            top: 20%;
            left: 15%;
            animation-delay: 0s;
        }
        .medium-mica-1 {
            width: 90px;
            height: 60px;
            top: 15%;
            right: 20%;
            animation-delay: 1s;
        }
        .medium-mica-2 {
            width: 100px;
            height: 70px;
            bottom: 25%;
            left: 25%;
            animation-delay: 2s;
        }
        .small-mica-1 {
            width: 60px;
            height: 40px;
            top: 60%;
            right: 15%;
            animation-delay: 0.5s;
        }
        .small-mica-2 {
            width: 50px;
            height: 35px;
            bottom: 15%;
            right: 35%;
            animation-delay: 1.5s;
        }
        .tiny-mica-1 {
            width: 30px;
            height: 20px;
            top: 45%;
            left: 60%;
            animation-delay: 2.5s;
        }
        .tiny-mica-2 {
            width: 25px;
            height: 18px;
            top: 75%;
            left: 70%;
            animation-delay: 3s;
        }
        .properties-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 40px 0;
        }
        .properties-card {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(243, 156, 18, 0.3);
        }
        .properties-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
            font-family: Arial, sans-serif;
        }
        .property-item {
            margin: 12px 0;
            font-size: 14px;
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        .property-label {
            font-weight: bold;
        }
        .crystal-structure {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 30px 0;
        }
        .structure-title {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
            font-family: Arial, sans-serif;
        }
        .crystal-diagram {
            width: 100%;
            height: 200px;
            background: linear-gradient(45deg, #ecf0f1 25%, transparent 25%), 
                        linear-gradient(-45deg, #ecf0f1 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #ecf0f1 75%), 
                        linear-gradient(-45deg, transparent 75%, #ecf0f1 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
            border: 2px solid #bdc3c7;
            border-radius: 10px;
            position: relative;
            margin: 20px 0;
        }
        .layer-representation {
            position: absolute;
            width: 80%;
            height: 8px;
            background: linear-gradient(90deg, #f39c12, #e67e22, #f39c12);
            left: 10%;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        .layer-1 { top: 20%; }
        .layer-2 { top: 35%; }
        .layer-3 { top: 50%; }
        .layer-4 { top: 65%; }
        .layer-5 { top: 80%; }
        .formation-info {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(52, 152, 219, 0.3);
            margin: 30px 0;
        }
        .formation-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
            font-family: Arial, sans-serif;
        }
        .formation-text {
            font-size: 14px;
            line-height: 1.6;
            font-family: Arial, sans-serif;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #f39c12;
            font-size: 12px;
            color: #7f8c8d;
            text-align: center;
            font-family: Arial, sans-serif;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Mica Mineral</h1>
            <div class="subtitle">Phyllosilicate Crystal Structure and Properties</div>
        </div>
        
        <div class="mica-showcase">
            <div class="mica-flake large-mica"></div>
            <div class="mica-flake medium-mica-1"></div>
            <div class="mica-flake medium-mica-2"></div>
            <div class="mica-flake small-mica-1"></div>
            <div class="mica-flake small-mica-2"></div>
            <div class="mica-flake tiny-mica-1"></div>
            <div class="mica-flake tiny-mica-2"></div>
        </div>
        
        <div class="properties-section">
            <div class="properties-card">
                <div class="properties-title">Physical Properties</div>
                <div class="property-item">
                    <span class="property-label">Hardness:</span>
                    <span>2-3 (Mohs scale)</span>
                </div>
                <div class="property-item">
                    <span class="property-label">Luster:</span>
                    <span>Metallic to pearly</span>
                </div>
                <div class="property-item">
                    <span class="property-label">Color:</span>
                    <span>Golden, silver, brown</span>
                </div>
                <div class="property-item">
                    <span class="property-label">Transparency:</span>
                    <span>Transparent to translucent</span>
                </div>
                <div class="property-item">
                    <span class="property-label">Cleavage:</span>
                    <span>Perfect basal {001}</span>
                </div>
                <div class="property-item">
                    <span class="property-label">Fracture:</span>
                    <span>Uneven to splintery</span>
                </div>
                <div class="property-item">
                    <span class="property-label">Specific Gravity:</span>
                    <span>2.7-3.2</span>
                </div>
            </div>
            
            <div class="properties-card">
                <div class="properties-title">Chemical Properties</div>
                <div class="property-item">
                    <span class="property-label">Chemical Formula:</span>
                    <span>KAl₂(AlSi₃O₁₀)(OH)₂</span>
                </div>
                <div class="property-item">
                    <span class="property-label">Crystal System:</span>
                    <span>Monoclinic</span>
                </div>
                <div class="property-item">
                    <span class="property-label">Mineral Group:</span>
                    <span>Phyllosilicate</span>
                </div>
                <div class="property-item">
                    <span class="property-label">Common Types:</span>
                    <span>Muscovite, Biotite</span>
                </div>
                <div class="property-item">
                    <span class="property-label">Composition:</span>
                    <span>K, Al, Si, O, H</span>
                </div>
                <div class="property-item">
                    <span class="property-label">Weathering:</span>
                    <span>Resistant to chemical</span>
                </div>
                <div class="property-item">
                    <span class="property-label">Stability:</span>
                    <span>High in surface conditions</span>
                </div>
            </div>
        </div>
        
        <div class="crystal-structure">
            <div class="structure-title">Layered Crystal Structure</div>
            <div class="crystal-diagram">
                <div class="layer-representation layer-1"></div>
                <div class="layer-representation layer-2"></div>
                <div class="layer-representation layer-3"></div>
                <div class="layer-representation layer-4"></div>
                <div class="layer-representation layer-5"></div>
            </div>
            <p style="text-align: center; color: #7f8c8d; font-family: Arial; font-size: 14px;">
                Perfect basal cleavage allows mica to split into thin, flexible sheets
            </p>
        </div>
        
        <div class="formation-info">
            <div class="formation-title">Formation and Occurrence</div>
            <div class="formation-text">
                Mica minerals form primarily in metamorphic rocks through the alteration of clay minerals under heat and pressure. 
                They are also common in igneous rocks, particularly granites and pegmatites. The perfect cleavage of mica results 
                from weak van der Waals forces between the silicate layers, allowing easy separation into thin, flexible sheets. 
                In soil deposits, mica flakes indicate the weathering and erosion of parent rocks containing these minerals. 
                Their resistance to chemical weathering makes them persistent components in sedimentary environments, often 
                concentrating in fine-grained deposits like silts and clays.
            </div>
        </div>
        
        <div class="footer">
            Detailed Mica Mineral Illustration | Scientific Visualization | Font: Arial
        </div>
    </div>
</body>
</html>
