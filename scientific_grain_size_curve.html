<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Particle Size Distribution Analysis - Scientific Research</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Times New Roman', serif;
            margin: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #333;
            line-height: 1.6;
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid transparent;
            border-image: linear-gradient(90deg, #667eea, #764ba2, #f093fb) 1;
            padding-bottom: 20px;
        }
        h1 {
            font-size: 24px;
            font-weight: bold;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }
        .subtitle {
            font-size: 16px;
            color: #7f8c8d;
            font-style: italic;
        }
        .chart-container {
            width: 100%;
            height: 700px;
            margin: 30px 0;
            border: 1px solid #bdc3c7;
            border-radius: 4px;
        }
        .data-section {
            margin-top: 40px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
            margin-top: 20px;
        }
        .data-table th, .data-table td {
            border: 1px solid #bdc3c7;
            padding: 8px;
            text-align: center;
        }
        .data-table th {
            background-color: #ecf0f1;
            font-weight: bold;
            color: #2c3e50;
        }
        .statistics {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        .statistics h3 {
            color: white;
            margin-top: 0;
            font-size: 16px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
        }
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            font-size: 14px;
        }
        .classification {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }
        .classification h3 {
            color: white;
            margin-top: 0;
            font-size: 16px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
        }
        .class-item {
            margin: 8px 0;
            font-size: 14px;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #bdc3c7;
            font-size: 12px;
            color: #7f8c8d;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Particle Size Distribution Analysis</h1>
            <div class="subtitle">Silty Sand Sample - Geotechnical Investigation</div>
        </div>

        <div id="chart" class="chart-container"></div>

        <div class="data-section">
            <div>
                <h3 style="color: #2c3e50; margin-bottom: 15px;">Statistical Analysis</h3>
                <div class="statistics">
                    <h3>Grain Size Parameters</h3>
                    <div class="stat-item">
                        <span>D₁₀ (mm):</span>
                        <span>0.08</span>
                    </div>
                    <div class="stat-item">
                        <span>D₃₀ (mm):</span>
                        <span>0.35</span>
                    </div>
                    <div class="stat-item">
                        <span>D₆₀ (mm):</span>
                        <span>2.8</span>
                    </div>
                    <div class="stat-item">
                        <span>Uniformity Coefficient (Cᵤ):</span>
                        <span>35.0</span>
                    </div>
                    <div class="stat-item">
                        <span>Coefficient of Curvature (Cᶜ):</span>
                        <span>0.55</span>
                    </div>
                </div>

                <div class="classification">
                    <h3>Soil Classification</h3>
                    <div class="class-item"><strong>USCS:</strong> SM (Silty Sand)</div>
                    <div class="class-item"><strong>AASHTO:</strong> A-2-4</div>
                    <div class="class-item"><strong>Gravel Content:</strong> 59.1%</div>
                    <div class="class-item"><strong>Sand Content:</strong> 19.3%</div>
                    <div class="class-item"><strong>Fines Content:</strong> 21.6%</div>
                </div>
            </div>

            <div>
                <h3 style="color: #2c3e50; margin-bottom: 15px;">Raw Data</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th rowspan="2">Soil Layer</th>
                            <th rowspan="2">Statistical Parameter</th>
                            <th colspan="6">Particle Size Fraction (%)</th>
                        </tr>
                        <tr>
                            <th>&gt;20.0 mm</th>
                            <th>20.0-2.00 mm</th>
                            <th>2.00-0.50 mm</th>
                            <th>0.50-0.25 mm</th>
                            <th>0.25-0.075 mm</th>
                            <th>&lt;0.075 mm</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td rowspan="4">Silty Sand</td>
                            <td>Mean</td>
                            <td>22.8</td>
                            <td>36.3</td>
                            <td>14.5</td>
                            <td>4.8</td>
                            <td>21.6</td>
                            <td>-</td>
                        </tr>
                        <tr>
                            <td>Sample Count</td>
                            <td>4</td>
                            <td>4</td>
                            <td>4</td>
                            <td>4</td>
                            <td>4</td>
                            <td>-</td>
                        </tr>
                        <tr>
                            <td>Maximum</td>
                            <td>38.5</td>
                            <td>43.2</td>
                            <td>17.3</td>
                            <td>6.3</td>
                            <td>31.1</td>
                            <td>-</td>
                        </tr>
                        <tr>
                            <td>Minimum</td>
                            <td>12.0</td>
                            <td>29.3</td>
                            <td>22.2</td>
                            <td>14.3</td>
                            <td>21.3</td>
                            <td>-</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="footer">
            Generated for Scientific Research | Geotechnical Engineering Analysis | Standards: ASTM D422, ASTM D6913
        </div>
    </div>

    <script>
        // Scientific data processing with enhanced data points
        const particleSizes = [100, 75, 50, 37.5, 25, 20.0, 19, 12.5, 9.5, 6.3, 4.75, 2.36, 2.0, 1.18, 0.6, 0.5, 0.3, 0.25, 0.15, 0.075, 0.05, 0.01];

        // Enhanced cumulative passing percentages with interpolated values
        const cumulativeMean = [100, 88.6, 82.4, 79.8, 78.5, 77.2, 76.8, 65.2, 58.7, 52.1, 47.3, 42.8, 40.9, 35.6, 30.2, 26.4, 24.8, 21.6, 18.4, 0, 0, 0];
        const cumulativeMax = [100, 93.8, 90.2, 88.9, 88.1, 87.5, 87.2, 72.4, 65.8, 58.9, 53.7, 48.9, 44.3, 38.2, 32.1, 27.0, 25.1, 20.7, 16.8, 0, 0, 0];
        const cumulativeMin = [100, 78.2, 69.8, 66.4, 63.7, 61.5, 61.0, 52.8, 46.2, 40.1, 35.9, 31.8, 32.2, 28.4, 24.6, 10.0, 8.2, 6.4, 4.8, 0, 0, 0];

        // Main curve (mean values) with enhanced styling
        const mainTrace = {
            x: particleSizes,
            y: cumulativeMean,
            mode: 'lines+markers+text',
            name: 'Mean Curve',
            line: {
                color: '#667eea',
                width: 4,
                shape: 'spline'
            },
            marker: {
                size: [12, 10, 8, 8, 8, 12, 8, 8, 10, 8, 10, 10, 12, 8, 8, 12, 8, 12, 8, 12, 8, 8],
                color: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff', '#5f27cd', '#00d2d3', '#ff9f43', '#10ac84', '#ee5a24', '#0abde3', '#006ba6', '#f368e0', '#ff3838', '#2ed573', '#ffa502', '#3742fa', '#2f3542', '#57606f', '#a4b0be'],
                line: {
                    color: '#667eea',
                    width: 2
                },
                symbol: ['circle', 'square', 'diamond', 'triangle-up', 'star', 'circle', 'square', 'diamond', 'triangle-up', 'star', 'circle', 'square', 'diamond', 'triangle-up', 'star', 'circle', 'square', 'diamond', 'triangle-up', 'star', 'circle', 'square']
            },
            text: cumulativeMean.map((value, i) => {
                // 只显示关键数据点的标注
                const keyIndices = [0, 5, 12, 15, 17, 19]; // 对应重要粒径的索引
                return keyIndices.includes(i) ? `${value}%` : '';
            }),
            textposition: 'top center',
            textfont: {
                size: 10,
                color: '#667eea',
                family: 'Times New Roman',
                style: 'bold'
            },
            hovertemplate: '<b>Particle Size:</b> %{x} mm<br><b>Passing:</b> %{y}%<extra></extra>'
        };

        // Confidence envelope with gradient
        const envelopeTrace = {
            x: particleSizes.concat(particleSizes.slice().reverse()),
            y: cumulativeMax.concat(cumulativeMin.slice().reverse()),
            fill: 'toself',
            fillcolor: 'rgba(102, 126, 234, 0.15)',
            line: {color: 'transparent'},
            name: 'Data Range',
            showlegend: true
        };

        // Maximum curve with enhanced styling
        const maxTrace = {
            x: particleSizes,
            y: cumulativeMax,
            mode: 'lines+markers+text',
            name: 'Maximum',
            line: {
                color: '#ff6b6b',
                width: 3,
                dash: 'dash',
                shape: 'spline'
            },
            marker: {
                size: 6,
                color: '#ff6b6b',
                symbol: 'triangle-up'
            },
            text: cumulativeMax.map((value, i) => i % 3 === 0 ? `${value}%` : ''),
            textposition: 'top center',
            textfont: {
                size: 8,
                color: '#ff6b6b',
                family: 'Times New Roman'
            },
            hovertemplate: '<b>Maximum:</b> %{y}% at %{x} mm<extra></extra>'
        };

        // Minimum curve with enhanced styling
        const minTrace = {
            x: particleSizes,
            y: cumulativeMin,
            mode: 'lines+markers+text',
            name: 'Minimum',
            line: {
                color: '#4ecdc4',
                width: 3,
                dash: 'dash',
                shape: 'spline'
            },
            marker: {
                size: 6,
                color: '#4ecdc4',
                symbol: 'triangle-down'
            },
            text: cumulativeMin.map((value, i) => i % 3 === 1 ? `${value}%` : ''),
            textposition: 'bottom center',
            textfont: {
                size: 8,
                color: '#4ecdc4',
                family: 'Times New Roman'
            },
            hovertemplate: '<b>Minimum:</b> %{y}% at %{x} mm<extra></extra>'
        };

        // Add particle size labels trace
        const sizeLabelsTrace = {
            x: [75, 20.0, 2.0, 0.5, 0.25, 0.075],
            y: [90, 85, 80, 75, 70, 65],
            mode: 'text',
            text: ['75mm', '20mm', '2mm', '0.5mm', '0.25mm', '0.075mm'],
            textposition: 'middle center',
            textfont: {
                size: 10,
                color: '#2c3e50',
                family: 'Times New Roman'
            },
            showlegend: false,
            hoverinfo: 'skip'
        };

        // Add D-value annotations
        const dValuesTrace = {
            x: [2.8, 0.35, 0.08],
            y: [60, 30, 10],
            mode: 'markers+text',
            text: ['D₆₀=2.8mm', 'D₃₀=0.35mm', 'D₁₀=0.08mm'],
            textposition: 'top right',
            textfont: {
                size: 11,
                color: '#e74c3c',
                family: 'Times New Roman',
                style: 'bold'
            },
            marker: {
                size: 8,
                color: '#e74c3c',
                symbol: 'diamond',
                line: {
                    color: 'white',
                    width: 2
                }
            },
            showlegend: false,
            name: 'Characteristic Diameters'
        };

        const data = [envelopeTrace, mainTrace, maxTrace, minTrace, sizeLabelsTrace, dValuesTrace];

        const layout = {
            title: {
                text: '',
                font: {
                    family: 'Times New Roman',
                    size: 16,
                    color: '#2c3e50'
                }
            },
            xaxis: {
                title: {
                    text: 'Particle Size (mm)',
                    font: {
                        family: 'Times New Roman',
                        size: 14,
                        color: '#2c3e50'
                    }
                },
                type: 'log',
                range: [Math.log10(0.01), Math.log10(100)],
                tickvals: [0.01, 0.075, 0.15, 0.3, 0.6, 1.18, 2.36, 4.75, 9.5, 19, 37.5, 75],
                ticktext: ['0.01', '0.075', '0.15', '0.30', '0.60', '1.18', '2.36', '4.75', '9.5', '19', '37.5', '75'],
                gridcolor: 'rgba(102, 126, 234, 0.3)',
                gridwidth: 1.5,
                zerolinecolor: 'rgba(102, 126, 234, 0.5)',
                tickfont: {
                    family: 'Times New Roman',
                    size: 12,
                    color: '#2c3e50'
                }
            },
            yaxis: {
                title: {
                    text: 'Percentage Passing (%)',
                    font: {
                        family: 'Times New Roman',
                        size: 14,
                        color: '#2c3e50'
                    }
                },
                range: [0, 100],
                dtick: 10,
                gridcolor: 'rgba(78, 205, 196, 0.3)',
                gridwidth: 1.5,
                zerolinecolor: 'rgba(78, 205, 196, 0.5)',
                tickfont: {
                    family: 'Times New Roman',
                    size: 12,
                    color: '#2c3e50'
                }
            },
            plot_bgcolor: 'rgba(248, 249, 250, 0.9)',
            paper_bgcolor: 'rgba(255, 255, 255, 0.95)',
            font: {
                family: 'Times New Roman',
                size: 12,
                color: '#2c3e50'
            },
            legend: {
                x: 0.02,
                y: 0.98,
                bgcolor: 'rgba(255,255,255,0.9)',
                bordercolor: '#bdc3c7',
                borderwidth: 1,
                font: {
                    family: 'Times New Roman',
                    size: 12,
                    color: '#2c3e50'
                }
            },
            shapes: [
                // Soil classification boundaries
                {
                    type: 'line',
                    x0: 0.075, x1: 0.075,
                    y0: 0, y1: 100,
                    line: {
                        color: '#e74c3c',
                        width: 1.5,
                        dash: 'dot'
                    }
                },
                {
                    type: 'line',
                    x0: 2.0, x1: 2.0,
                    y0: 0, y1: 100,
                    line: {
                        color: '#e74c3c',
                        width: 1.5,
                        dash: 'dot'
                    }
                },
                {
                    type: 'line',
                    x0: 20.0, x1: 20.0,
                    y0: 0, y1: 100,
                    line: {
                        color: '#e74c3c',
                        width: 1.5,
                        dash: 'dot'
                    }
                }
            ],
            annotations: [
                {
                    x: Math.log10(0.03),
                    y: 5,
                    text: 'Fines',
                    showarrow: false,
                    font: {
                        family: 'Times New Roman',
                        size: 11,
                        color: 'white'
                    },
                    bgcolor: 'rgba(102, 126, 234, 0.9)',
                    bordercolor: 'rgba(67, 56, 202, 0.8)',
                    borderwidth: 2,
                    borderradius: 6
                },
                {
                    x: Math.log10(0.4),
                    y: 5,
                    text: 'Sand',
                    showarrow: false,
                    font: {
                        family: 'Times New Roman',
                        size: 11,
                        color: 'white'
                    },
                    bgcolor: 'rgba(255, 193, 7, 0.9)',
                    bordercolor: 'rgba(255, 152, 0, 0.8)',
                    borderwidth: 2,
                    borderradius: 6
                },
                {
                    x: Math.log10(6),
                    y: 5,
                    text: 'Gravel',
                    showarrow: false,
                    font: {
                        family: 'Times New Roman',
                        size: 11,
                        color: 'white'
                    },
                    bgcolor: 'rgba(255, 107, 107, 0.9)',
                    bordercolor: 'rgba(244, 67, 54, 0.8)',
                    borderwidth: 2,
                    borderradius: 6
                },
                {
                    x: Math.log10(40),
                    y: 5,
                    text: 'Cobbles',
                    showarrow: false,
                    font: {
                        family: 'Times New Roman',
                        size: 11,
                        color: 'white'
                    },
                    bgcolor: 'rgba(78, 205, 196, 0.9)',
                    bordercolor: 'rgba(0, 150, 136, 0.8)',
                    borderwidth: 2,
                    borderradius: 6
                }
            ]
        };

        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToAdd: ['downloadPlot'],
            toImageButtonOptions: {
                format: 'png',
                filename: 'particle_size_distribution_scientific',
                height: 700,
                width: 1200,
                scale: 2
            }
        };

        Plotly.newPlot('chart', data, layout, config);
    </script>
</body>
</html>
