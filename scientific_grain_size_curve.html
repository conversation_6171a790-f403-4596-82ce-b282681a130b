<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Particle Size Distribution Analysis - Scientific Research</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Times New Roman', serif;
            margin: 20px;
            background-color: #fafafa;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 2px solid #2c3e50;
            padding-bottom: 20px;
        }
        h1 {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .subtitle {
            font-size: 16px;
            color: #7f8c8d;
            font-style: italic;
        }
        .chart-container {
            width: 100%;
            height: 700px;
            margin: 30px 0;
            border: 1px solid #bdc3c7;
            border-radius: 4px;
        }
        .data-section {
            margin-top: 40px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
            margin-top: 20px;
        }
        .data-table th, .data-table td {
            border: 1px solid #bdc3c7;
            padding: 8px;
            text-align: center;
        }
        .data-table th {
            background-color: #ecf0f1;
            font-weight: bold;
            color: #2c3e50;
        }
        .statistics {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 4px;
            border-left: 4px solid #3498db;
        }
        .statistics h3 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 16px;
        }
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            font-size: 14px;
        }
        .classification {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 4px;
            border-left: 4px solid #e74c3c;
        }
        .classification h3 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 16px;
        }
        .class-item {
            margin: 8px 0;
            font-size: 14px;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #bdc3c7;
            font-size: 12px;
            color: #7f8c8d;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Particle Size Distribution Analysis</h1>
            <div class="subtitle">Silty Sand Sample - Geotechnical Investigation</div>
        </div>
        
        <div id="chart" class="chart-container"></div>
        
        <div class="data-section">
            <div>
                <h3 style="color: #2c3e50; margin-bottom: 15px;">Statistical Analysis</h3>
                <div class="statistics">
                    <h3>Grain Size Parameters</h3>
                    <div class="stat-item">
                        <span>D₁₀ (mm):</span>
                        <span>0.08</span>
                    </div>
                    <div class="stat-item">
                        <span>D₃₀ (mm):</span>
                        <span>0.35</span>
                    </div>
                    <div class="stat-item">
                        <span>D₆₀ (mm):</span>
                        <span>2.8</span>
                    </div>
                    <div class="stat-item">
                        <span>Uniformity Coefficient (Cᵤ):</span>
                        <span>35.0</span>
                    </div>
                    <div class="stat-item">
                        <span>Coefficient of Curvature (Cᶜ):</span>
                        <span>0.55</span>
                    </div>
                </div>
                
                <div class="classification">
                    <h3>Soil Classification</h3>
                    <div class="class-item"><strong>USCS:</strong> SM (Silty Sand)</div>
                    <div class="class-item"><strong>AASHTO:</strong> A-2-4</div>
                    <div class="class-item"><strong>Gravel Content:</strong> 59.1%</div>
                    <div class="class-item"><strong>Sand Content:</strong> 19.3%</div>
                    <div class="class-item"><strong>Fines Content:</strong> 21.6%</div>
                </div>
            </div>
            
            <div>
                <h3 style="color: #2c3e50; margin-bottom: 15px;">Raw Data</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th rowspan="2">Soil Layer</th>
                            <th rowspan="2">Statistical Parameter</th>
                            <th colspan="6">Particle Size Fraction (%)</th>
                        </tr>
                        <tr>
                            <th>&gt;20.0 mm</th>
                            <th>20.0-2.00 mm</th>
                            <th>2.00-0.50 mm</th>
                            <th>0.50-0.25 mm</th>
                            <th>0.25-0.075 mm</th>
                            <th>&lt;0.075 mm</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td rowspan="4">Silty Sand</td>
                            <td>Mean</td>
                            <td>22.8</td>
                            <td>36.3</td>
                            <td>14.5</td>
                            <td>4.8</td>
                            <td>21.6</td>
                            <td>-</td>
                        </tr>
                        <tr>
                            <td>Sample Count</td>
                            <td>4</td>
                            <td>4</td>
                            <td>4</td>
                            <td>4</td>
                            <td>4</td>
                            <td>-</td>
                        </tr>
                        <tr>
                            <td>Maximum</td>
                            <td>38.5</td>
                            <td>43.2</td>
                            <td>17.3</td>
                            <td>6.3</td>
                            <td>31.1</td>
                            <td>-</td>
                        </tr>
                        <tr>
                            <td>Minimum</td>
                            <td>12.0</td>
                            <td>29.3</td>
                            <td>22.2</td>
                            <td>14.3</td>
                            <td>21.3</td>
                            <td>-</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="footer">
            Generated for Scientific Research | Geotechnical Engineering Analysis | Standards: ASTM D422, ASTM D6913
        </div>
    </div>

    <script>
        // Scientific data processing
        const particleSizes = [100, 20.0, 2.0, 0.5, 0.25, 0.075, 0.01];
        
        // Cumulative passing percentages calculated from retained percentages
        const cumulativeMean = [100, 77.2, 40.9, 26.4, 21.6, 0, 0];
        const cumulativeMax = [100, 87.5, 44.3, 27.0, 20.7, 0, 0];
        const cumulativeMin = [100, 61.5, 32.2, 10.0, 6.4, 0, 0];
        
        // Main curve (mean values)
        const mainTrace = {
            x: particleSizes,
            y: cumulativeMean,
            mode: 'lines+markers',
            name: 'Mean Curve',
            line: {
                color: '#2c3e50',
                width: 3,
                shape: 'linear'
            },
            marker: {
                size: 8,
                color: 'white',
                line: {
                    color: '#2c3e50',
                    width: 2
                },
                symbol: 'circle'
            }
        };
        
        // Confidence envelope
        const envelopeTrace = {
            x: particleSizes.concat(particleSizes.slice().reverse()),
            y: cumulativeMax.concat(cumulativeMin.slice().reverse()),
            fill: 'toself',
            fillcolor: 'rgba(52, 73, 94, 0.1)',
            line: {color: 'transparent'},
            name: 'Data Range',
            showlegend: true
        };
        
        // Maximum curve
        const maxTrace = {
            x: particleSizes,
            y: cumulativeMax,
            mode: 'lines',
            name: 'Maximum',
            line: {
                color: '#e74c3c',
                width: 2,
                dash: 'dash'
            }
        };
        
        // Minimum curve
        const minTrace = {
            x: particleSizes,
            y: cumulativeMin,
            mode: 'lines',
            name: 'Minimum',
            line: {
                color: '#3498db',
                width: 2,
                dash: 'dash'
            }
        };
        
        const data = [envelopeTrace, mainTrace, maxTrace, minTrace];
        
        const layout = {
            title: {
                text: '',
                font: {
                    family: 'Times New Roman',
                    size: 16,
                    color: '#2c3e50'
                }
            },
            xaxis: {
                title: {
                    text: 'Particle Size (mm)',
                    font: {
                        family: 'Times New Roman',
                        size: 14,
                        color: '#2c3e50'
                    }
                },
                type: 'log',
                range: [Math.log10(0.01), Math.log10(100)],
                tickvals: [0.01, 0.075, 0.15, 0.3, 0.6, 1.18, 2.36, 4.75, 9.5, 19, 37.5, 75],
                ticktext: ['0.01', '0.075', '0.15', '0.30', '0.60', '1.18', '2.36', '4.75', '9.5', '19', '37.5', '75'],
                gridcolor: '#bdc3c7',
                gridwidth: 1,
                zerolinecolor: '#95a5a6',
                tickfont: {
                    family: 'Times New Roman',
                    size: 12,
                    color: '#2c3e50'
                }
            },
            yaxis: {
                title: {
                    text: 'Percentage Passing (%)',
                    font: {
                        family: 'Times New Roman',
                        size: 14,
                        color: '#2c3e50'
                    }
                },
                range: [0, 100],
                dtick: 10,
                gridcolor: '#bdc3c7',
                gridwidth: 1,
                zerolinecolor: '#95a5a6',
                tickfont: {
                    family: 'Times New Roman',
                    size: 12,
                    color: '#2c3e50'
                }
            },
            plot_bgcolor: 'white',
            paper_bgcolor: 'white',
            font: {
                family: 'Times New Roman',
                size: 12,
                color: '#2c3e50'
            },
            legend: {
                x: 0.02,
                y: 0.98,
                bgcolor: 'rgba(255,255,255,0.9)',
                bordercolor: '#bdc3c7',
                borderwidth: 1,
                font: {
                    family: 'Times New Roman',
                    size: 12,
                    color: '#2c3e50'
                }
            },
            shapes: [
                // Soil classification boundaries
                {
                    type: 'line',
                    x0: 0.075, x1: 0.075,
                    y0: 0, y1: 100,
                    line: {
                        color: '#e74c3c',
                        width: 1.5,
                        dash: 'dot'
                    }
                },
                {
                    type: 'line',
                    x0: 2.0, x1: 2.0,
                    y0: 0, y1: 100,
                    line: {
                        color: '#e74c3c',
                        width: 1.5,
                        dash: 'dot'
                    }
                },
                {
                    type: 'line',
                    x0: 20.0, x1: 20.0,
                    y0: 0, y1: 100,
                    line: {
                        color: '#e74c3c',
                        width: 1.5,
                        dash: 'dot'
                    }
                }
            ],
            annotations: [
                {
                    x: Math.log10(0.03),
                    y: 5,
                    text: 'Fines',
                    showarrow: false,
                    font: {
                        family: 'Times New Roman',
                        size: 11,
                        color: '#2c3e50'
                    },
                    bgcolor: 'rgba(255,255,255,0.8)',
                    bordercolor: '#bdc3c7',
                    borderwidth: 1
                },
                {
                    x: Math.log10(0.4),
                    y: 5,
                    text: 'Sand',
                    showarrow: false,
                    font: {
                        family: 'Times New Roman',
                        size: 11,
                        color: '#2c3e50'
                    },
                    bgcolor: 'rgba(255,255,255,0.8)',
                    bordercolor: '#bdc3c7',
                    borderwidth: 1
                },
                {
                    x: Math.log10(6),
                    y: 5,
                    text: 'Gravel',
                    showarrow: false,
                    font: {
                        family: 'Times New Roman',
                        size: 11,
                        color: '#2c3e50'
                    },
                    bgcolor: 'rgba(255,255,255,0.8)',
                    bordercolor: '#bdc3c7',
                    borderwidth: 1
                },
                {
                    x: Math.log10(40),
                    y: 5,
                    text: 'Cobbles',
                    showarrow: false,
                    font: {
                        family: 'Times New Roman',
                        size: 11,
                        color: '#2c3e50'
                    },
                    bgcolor: 'rgba(255,255,255,0.8)',
                    bordercolor: '#bdc3c7',
                    borderwidth: 1
                }
            ]
        };
        
        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToAdd: ['downloadPlot'],
            toImageButtonOptions: {
                format: 'png',
                filename: 'particle_size_distribution_scientific',
                height: 700,
                width: 1200,
                scale: 2
            }
        };
        
        Plotly.newPlot('chart', data, layout, config);
    </script>
</body>
</html>
