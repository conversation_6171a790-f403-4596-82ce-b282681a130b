<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Particle Size Distribution Curve - New Sample</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 25%, #ffecd2 50%, #fcb69f 75%, #ff9a9e 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: linear-gradient(145deg, rgba(255,255,255,0.95) 0%, rgba(248,249,250,0.9) 100%);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid transparent;
            border-image: linear-gradient(90deg, #ff9a9e, #fecfef, #ffecd2) 1;
        }
        h1 {
            font-size: 28px;
            font-weight: bold;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24, #ff9ff3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
            font-family: Arial, sans-serif;
        }
        .subtitle {
            font-size: 16px;
            color: #7f8c8d;
            font-style: italic;
            font-family: Arial, sans-serif;
        }
        .chart-container {
            width: 100%;
            height: 700px;
            margin: 30px 0;
            background: linear-gradient(135deg, 
                rgba(255, 154, 158, 0.08) 0%, 
                rgba(254, 207, 239, 0.08) 25%,
                rgba(255, 236, 210, 0.08) 50%,
                rgba(252, 182, 159, 0.08) 75%,
                rgba(255, 183, 197, 0.08) 100%);
            border-radius: 15px;
            padding: 20px;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.05);
            border: 1px solid rgba(255, 154, 158, 0.2);
        }
        .data-section {
            margin-top: 40px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        .statistics {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
        }
        .statistics h3 {
            color: white;
            margin-top: 0;
            font-size: 18px;
            font-family: Arial, sans-serif;
        }
        .classification {
            background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(254, 202, 87, 0.3);
        }
        .classification h3 {
            color: white;
            margin-top: 0;
            font-size: 18px;
            font-family: Arial, sans-serif;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
            margin-top: 20px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            font-family: Arial, sans-serif;
        }
        .data-table th, .data-table td {
            border: 1px solid #e0e0e0;
            padding: 12px;
            text-align: center;
        }
        .data-table th {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            font-weight: bold;
        }
        .data-table td {
            background: rgba(255,255,255,0.8);
        }
        .data-table tr:nth-child(even) td {
            background: rgba(248,249,250,0.8);
        }
        .stat-item, .class-item {
            margin: 10px 0;
            font-size: 14px;
            font-family: Arial, sans-serif;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid transparent;
            border-image: linear-gradient(90deg, #ff9a9e, #fecfef) 1;
            font-size: 12px;
            color: #7f8c8d;
            text-align: center;
            font-family: Arial, sans-serif;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Particle Size Distribution Curve</h1>
            <div class="subtitle">New Sample - Sieve Analysis Results</div>
        </div>
        
        <div id="chart" class="chart-container"></div>
        
        <div class="data-section">
            <div>
                <div class="statistics">
                    <h3>Grain Size Parameters</h3>
                    <div class="stat-item"><strong>D₁₀:</strong> 0.16 mm</div>
                    <div class="stat-item"><strong>D₃₀:</strong> 0.28 mm</div>
                    <div class="stat-item"><strong>D₆₀:</strong> 0.45 mm</div>
                    <div class="stat-item"><strong>Uniformity Coefficient (Cᵤ):</strong> 2.81</div>
                    <div class="stat-item"><strong>Coefficient of Curvature (Cᶜ):</strong> 1.09</div>
                    <div class="stat-item"><strong>Classification:</strong> Well-graded Sand</div>
                </div>
                
                <div class="classification">
                    <h3>Soil Classification</h3>
                    <div class="class-item"><strong>USCS:</strong> SW (Well-graded Sand)</div>
                    <div class="class-item"><strong>Gravel Content:</strong> 5.69%</div>
                    <div class="class-item"><strong>Sand Content:</strong> 93.83%</div>
                    <div class="class-item"><strong>Fines Content:</strong> 0.48%</div>
                    <div class="class-item"><strong>Dominant Size:</strong> Medium Sand</div>
                </div>
            </div>
            
            <div>
                <h3 style="color: #2c3e50; margin-bottom: 15px; font-family: Arial, sans-serif;">Sieve Analysis Data</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Sieve Size (mm)</th>
                            <th>Retained (%)</th>
                            <th>Cumulative Retained (%)</th>
                            <th>Passing (%)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>&gt;2.0</td>
                            <td>0.137</td>
                            <td>0.137</td>
                            <td>99.863</td>
                        </tr>
                        <tr>
                            <td>2.0-1.0</td>
                            <td>5.55</td>
                            <td>5.687</td>
                            <td>94.313</td>
                        </tr>
                        <tr>
                            <td>1.0-0.5</td>
                            <td>32.48</td>
                            <td>38.167</td>
                            <td>61.833</td>
                        </tr>
                        <tr>
                            <td>0.5-0.25</td>
                            <td>32.19</td>
                            <td>70.357</td>
                            <td>29.643</td>
                        </tr>
                        <tr>
                            <td>0.25-0.125</td>
                            <td>21.61</td>
                            <td>91.967</td>
                            <td>8.033</td>
                        </tr>
                        <tr>
                            <td>0.125-0.075</td>
                            <td>7.55</td>
                            <td>99.517</td>
                            <td>0.483</td>
                        </tr>
                        <tr>
                            <td>&lt;0.063</td>
                            <td>0.48</td>
                            <td>99.997</td>
                            <td>0.003</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="footer">
            Generated with New Sample Sieve Analysis Data | Standards: ASTM D422, ASTM D6913 | Font: Arial
        </div>
    </div>

    <script>
        // New sample data from sieve analysis
        const particleSizes = [10, 2.0, 1.0, 0.5, 0.25, 0.125, 0.075, 0.063, 0.01];
        
        // Retained percentages from the new data
        const retainedPercentages = [0, 0.137, 5.55, 32.48, 32.19, 21.61, 7.55, 0.48, 0];
        
        // Calculate cumulative passing percentages
        let cumulativeRetained = 0;
        const cumulativePassing = [];
        
        for (let i = 0; i < particleSizes.length; i++) {
            if (i === 0) {
                cumulativePassing.push(100);
            } else {
                cumulativeRetained += retainedPercentages[i];
                cumulativePassing.push(100 - cumulativeRetained);
            }
        }
        
        // Ensure last point approaches 0
        cumulativePassing[cumulativePassing.length - 1] = 0;
        
        // Main curve with data labels
        const mainTrace = {
            x: particleSizes,
            y: cumulativePassing,
            mode: 'lines+markers+text',
            name: 'Grain Size Curve',
            line: {
                color: '#ff6b6b',
                width: 4,
                shape: 'linear'
            },
            marker: {
                size: [12, 14, 16, 18, 16, 14, 12, 10, 8],
                color: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff', '#5f27cd', '#2f3542'],
                line: {
                    color: '#ff6b6b',
                    width: 3
                },
                symbol: ['circle', 'square', 'diamond', 'triangle-up', 'star', 'hexagon', 'cross', 'pentagon', 'circle']
            },
            text: cumulativePassing.map((value, i) => {
                // Show labels for key points
                const keyIndices = [0, 1, 2, 3, 4, 5, 6, 7];
                return keyIndices.includes(i) ? `${value.toFixed(1)}%` : '';
            }),
            textposition: 'top center',
            textfont: {
                size: 11,
                color: '#ff6b6b',
                family: 'Arial',
                style: 'bold'
            },
            hovertemplate: '<b>Particle Size:</b> %{x} mm<br><b>Passing:</b> %{y:.3f}%<extra></extra>'
        };
        
        // Add characteristic diameter markers
        const dValuesTrace = {
            x: [0.45, 0.28, 0.16],
            y: [60, 30, 10],
            mode: 'markers+text',
            text: ['D₆₀=0.45mm', 'D₃₀=0.28mm', 'D₁₀=0.16mm'],
            textposition: 'top right',
            textfont: {
                size: 12,
                color: '#e74c3c',
                family: 'Arial',
                style: 'bold'
            },
            marker: {
                size: 10,
                color: '#e74c3c',
                symbol: 'diamond',
                line: {
                    color: 'white',
                    width: 2
                }
            },
            showlegend: false,
            name: 'Characteristic Diameters',
            hovertemplate: '<b>%{text}</b><br>Passing: %{y}%<extra></extra>'
        };
        
        const data = [mainTrace, dValuesTrace];
        
        const layout = {
            title: {
                text: 'Particle Size Distribution Curve - New Sample',
                font: {
                    family: 'Arial',
                    size: 18,
                    color: '#2c3e50'
                }
            },
            xaxis: {
                title: {
                    text: 'Particle Size (mm)',
                    font: {
                        family: 'Arial',
                        size: 16,
                        color: '#2c3e50'
                    }
                },
                type: 'log',
                range: [Math.log10(0.01), Math.log10(10)],
                tickvals: [0.01, 0.063, 0.075, 0.125, 0.25, 0.5, 1.0, 2.0, 10],
                ticktext: ['0.01', '0.063', '0.075', '0.125', '0.25', '0.5', '1.0', '2.0', '10'],
                gridcolor: 'rgba(255, 107, 107, 0.3)',
                gridwidth: 2,
                tickfont: {
                    family: 'Arial',
                    size: 12,
                    color: '#2c3e50'
                }
            },
            yaxis: {
                title: {
                    text: 'Percentage Passing (%)',
                    font: {
                        family: 'Arial',
                        size: 16,
                        color: '#2c3e50'
                    }
                },
                range: [0, 100],
                dtick: 10,
                gridcolor: 'rgba(254, 202, 87, 0.3)',
                gridwidth: 2,
                tickfont: {
                    family: 'Arial',
                    size: 12,
                    color: '#2c3e50'
                }
            },
            plot_bgcolor: 'rgba(255, 255, 255, 0.8)',
            paper_bgcolor: 'rgba(255, 255, 255, 0.9)',
            font: {
                family: 'Arial',
                size: 12,
                color: '#2c3e50'
            },
            legend: {
                x: 0.02,
                y: 0.98,
                bgcolor: 'rgba(255,255,255,0.9)',
                bordercolor: 'rgba(255, 107, 107, 0.5)',
                borderwidth: 2,
                font: {
                    family: 'Arial',
                    size: 12,
                    color: '#2c3e50'
                }
            },
            shapes: [
                // Soil classification boundaries
                {
                    type: 'line',
                    x0: 0.075, x1: 0.075,
                    y0: 0, y1: 100,
                    line: {
                        color: '#ff6b6b',
                        width: 3,
                        dash: 'dot'
                    }
                },
                {
                    type: 'line',
                    x0: 2.0, x1: 2.0,
                    y0: 0, y1: 100,
                    line: {
                        color: '#ff6b6b',
                        width: 3,
                        dash: 'dot'
                    }
                }
            ],
            annotations: [
                {
                    x: Math.log10(0.03),
                    y: 10,
                    text: 'Fines<br>(<0.075mm)',
                    showarrow: false,
                    font: {
                        family: 'Arial',
                        size: 11,
                        color: 'white'
                    },
                    bgcolor: 'rgba(255, 107, 107, 0.9)',
                    bordercolor: 'rgba(238, 90, 36, 0.8)',
                    borderwidth: 2,
                    borderradius: 8
                },
                {
                    x: Math.log10(0.5),
                    y: 10,
                    text: 'Sand<br>(0.075-2mm)',
                    showarrow: false,
                    font: {
                        family: 'Arial',
                        size: 11,
                        color: 'white'
                    },
                    bgcolor: 'rgba(254, 202, 87, 0.9)',
                    bordercolor: 'rgba(255, 159, 243, 0.8)',
                    borderwidth: 2,
                    borderradius: 8
                },
                {
                    x: Math.log10(5),
                    y: 10,
                    text: 'Gravel<br>(>2mm)',
                    showarrow: false,
                    font: {
                        family: 'Arial',
                        size: 11,
                        color: 'white'
                    },
                    bgcolor: 'rgba(255, 159, 243, 0.9)',
                    bordercolor: 'rgba(254, 202, 87, 0.8)',
                    borderwidth: 2,
                    borderradius: 8
                }
            ]
        };
        
        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToAdd: ['downloadPlot'],
            toImageButtonOptions: {
                format: 'png',
                filename: 'new_sample_particle_size_distribution',
                height: 700,
                width: 1200,
                scale: 2
            }
        };
        
        Plotly.newPlot('chart', data, layout, config);
    </script>
</body>
</html>
