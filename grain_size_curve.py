#!/usr/bin/env python3
"""
Particle Size Distribution Curve Generator
Based on the provided soil data for Silty Sand
"""

import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import numpy as np

def create_particle_size_curve():
    """Create a professional particle size distribution curve"""
    
    # Set Arial font
    plt.rcParams['font.family'] = 'Arial'
    plt.rcParams['font.size'] = 11
    
    # Data from the table (converted to cumulative passing percentages)
    # Sieve sizes in mm (from coarse to fine)
    sieve_sizes = np.array([100, 20.0, 2.0, 0.5, 0.25, 0.075, 0.01])
    
    # Cumulative passing percentages (calculated from your data)
    # Starting from 100% and subtracting retained percentages
    cumulative_mean = np.array([100, 77.2, 40.9, 26.4, 21.6, 0, 0])
    cumulative_max = np.array([100, 87.5, 44.3, 27.0, 20.7, 0, 0])
    cumulative_min = np.array([100, 61.5, 32.2, 10.0, 6.4, 0, 0])
    
    # Create figure with professional styling
    fig, ax = plt.subplots(figsize=(12, 8))
    fig.patch.set_facecolor('white')
    
    # Plot the main curve (average)
    ax.semilogx(sieve_sizes, cumulative_mean, 
               'o-', linewidth=3, markersize=8, 
               color='#1f77b4', markerfacecolor='white', 
               markeredgewidth=2, markeredgecolor='#1f77b4',
               label='Average', zorder=3)
    
    # Plot the range envelope
    ax.fill_between(sieve_sizes, cumulative_min, cumulative_max,
                   alpha=0.25, color='#ff7f0e', 
                   label='Range (Min-Max)', zorder=1)
    
    # Plot max and min curves
    ax.semilogx(sieve_sizes, cumulative_max, 
               '--', linewidth=2, color='#d62728', 
               alpha=0.8, label='Maximum', zorder=2)
    
    ax.semilogx(sieve_sizes, cumulative_min, 
               '--', linewidth=2, color='#2ca02c', 
               alpha=0.8, label='Minimum', zorder=2)
    
    # Customize axes
    ax.set_xlabel('Particle Size (mm)', fontsize=14, fontweight='bold')
    ax.set_ylabel('Percentage Passing (%)', fontsize=14, fontweight='bold')
    ax.set_title('Particle Size Distribution Curve\nSilty Sand', 
                fontsize=16, fontweight='bold', pad=20)
    
    # Set axis limits
    ax.set_xlim(0.01, 100)
    ax.set_ylim(0, 100)
    
    # Add grid
    ax.grid(True, which="major", linestyle='-', alpha=0.4, color='gray')
    ax.grid(True, which="minor", linestyle=':', alpha=0.2, color='gray')
    
    # Set standard sieve sizes for x-axis
    standard_sieves = [0.075, 0.15, 0.3, 0.6, 1.18, 2.36, 4.75, 9.5, 19, 37.5, 75]
    ax.set_xticks(standard_sieves)
    ax.set_xticklabels([f'{s:.3f}' if s < 1 else f'{s:.1f}' for s in standard_sieves])
    
    # Y-axis ticks
    ax.set_yticks(np.arange(0, 101, 10))
    
    # Add classification lines
    ax.axvline(x=0.075, color='red', linestyle=':', alpha=0.6, linewidth=1.5)
    ax.axvline(x=2.0, color='red', linestyle=':', alpha=0.6, linewidth=1.5)
    ax.axvline(x=20.0, color='red', linestyle=':', alpha=0.6, linewidth=1.5)
    
    # Add soil classification annotations
    ax.text(0.03, 95, 'Fines\n(<0.075mm)', ha='center', va='top', 
           fontsize=9, bbox=dict(boxstyle="round,pad=0.3", 
           facecolor='lightblue', alpha=0.8, edgecolor='navy'))
    
    ax.text(0.4, 95, 'Sand\n(0.075-2mm)', ha='center', va='top', 
           fontsize=9, bbox=dict(boxstyle="round,pad=0.3", 
           facecolor='lightyellow', alpha=0.8, edgecolor='orange'))
    
    ax.text(6, 95, 'Gravel\n(2-20mm)', ha='center', va='top', 
           fontsize=9, bbox=dict(boxstyle="round,pad=0.3", 
           facecolor='lightcoral', alpha=0.8, edgecolor='darkred'))
    
    ax.text(40, 95, 'Cobbles\n(>20mm)', ha='center', va='top', 
           fontsize=9, bbox=dict(boxstyle="round,pad=0.3", 
           facecolor='lightgreen', alpha=0.8, edgecolor='darkgreen'))
    
    # Add legend
    legend = ax.legend(loc='center right', frameon=True, 
                      fancybox=True, shadow=True, fontsize=11)
    legend.get_frame().set_facecolor('white')
    legend.get_frame().set_alpha(0.9)
    
    # Improve layout
    plt.tight_layout()
    
    # Save the plot in multiple formats
    plt.savefig('particle_size_distribution.png', dpi=300, 
               bbox_inches='tight', facecolor='white', edgecolor='none')
    plt.savefig('particle_size_distribution.pdf', 
               bbox_inches='tight', facecolor='white', edgecolor='none')
    
    print("✓ Particle size distribution curve generated successfully!")
    print("✓ Files saved:")
    print("  - particle_size_distribution.png (high resolution)")
    print("  - particle_size_distribution.pdf (vector format)")
    
    return fig, ax

if __name__ == "__main__":
    try:
        create_particle_size_curve()
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
