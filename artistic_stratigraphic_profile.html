<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Artistic Stratigraphic Profile</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 25%, #ffecd2 50%, #fcb69f 75%, #ff9a9e 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #2c3e50;
        }
        h1 {
            font-size: 24px;
            color: #2c3e50;
            margin-bottom: 10px;
            font-family: Arial, sans-serif;
        }
        .subtitle {
            font-size: 16px;
            color: #7f8c8d;
            font-style: italic;
            font-family: Arial, sans-serif;
        }
        .profile-section {
            display: flex;
            gap: 30px;
            margin-top: 30px;
        }
        .soil-profile {
            width: 300px;
            height: 600px;
            border: 3px solid #2c3e50;
            border-radius: 10px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .ground-surface {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 40px;
            background: linear-gradient(to bottom, #228B22, #32CD32, #90EE90);
            border-bottom: 2px solid #2d5016;
        }
        .grass {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 15px;
            background: repeating-linear-gradient(
                90deg,
                #228B22 0px,
                #32CD32 2px,
                #228B22 4px
            );
        }
        .silt-layer {
            position: absolute;
            top: 40px;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                to bottom,
                #8B7355 0%,
                #A0826D 20%,
                #654321 40%,
                #8B7355 60%,
                #A0826D 80%,
                #654321 100%
            );
            opacity: 0.9;
        }
        .silt-texture {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                radial-gradient(circle at 20% 30%, rgba(139, 115, 85, 0.3) 2px, transparent 2px),
                radial-gradient(circle at 60% 70%, rgba(160, 130, 109, 0.4) 1px, transparent 1px),
                radial-gradient(circle at 80% 20%, rgba(101, 67, 33, 0.3) 1.5px, transparent 1.5px);
            background-size: 30px 30px, 20px 20px, 25px 25px;
        }
        .mica-particles {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }
        .mica {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #FFD700;
            border-radius: 50%;
            box-shadow: 0 0 3px #B8860B;
            animation: shimmer 3s ease-in-out infinite;
        }
        @keyframes shimmer {
            0%, 100% { opacity: 0.6; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }
        .shell {
            position: absolute;
            width: 12px;
            height: 10px;
            background: #F5F5DC;
            border: 1px solid #8B7355;
            border-radius: 50%;
            box-shadow: inset 0 1px 2px rgba(139, 115, 85, 0.3);
        }
        .sand-pocket {
            position: absolute;
            background: rgba(222, 184, 135, 0.7);
            border: 1px dashed #DEB887;
            border-radius: 8px;
        }
        .groundwater {
            position: absolute;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #1e90ff, #00bfff, #1e90ff);
            animation: flow 2s ease-in-out infinite;
        }
        @keyframes flow {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }
        .description-panel {
            flex: 1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
        .description-panel h3 {
            margin-top: 0;
            font-size: 20px;
            font-family: Arial, sans-serif;
        }
        .property {
            margin: 15px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            font-family: Arial, sans-serif;
        }
        .property-title {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 5px;
        }
        .property-desc {
            font-size: 13px;
            opacity: 0.9;
        }
        .legend {
            margin-top: 30px;
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .legend h3 {
            color: #2c3e50;
            margin-top: 0;
            font-family: Arial, sans-serif;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin: 12px 0;
            font-family: Arial, sans-serif;
        }
        .legend-symbol {
            width: 30px;
            height: 20px;
            margin-right: 15px;
            border: 1px solid #2c3e50;
            border-radius: 3px;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 12px;
            color: #7f8c8d;
            font-family: Arial, sans-serif;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Stratigraphic Profile - Layer 1</h1>
            <div class="subtitle">Saturated Silt with Organic Inclusions</div>
        </div>
        
        <div class="profile-section">
            <div class="soil-profile">
                <!-- Ground Surface -->
                <div class="ground-surface">
                    <div class="grass"></div>
                </div>
                
                <!-- Silt Layer -->
                <div class="silt-layer">
                    <div class="silt-texture"></div>
                </div>
                
                <!-- Groundwater Level -->
                <div class="groundwater" style="top: 60px;"></div>
                
                <!-- Mica Particles -->
                <div class="mica-particles">
                    <div class="mica" style="top: 80px; left: 20px;"></div>
                    <div class="mica" style="top: 120px; left: 60px;"></div>
                    <div class="mica" style="top: 160px; left: 40px;"></div>
                    <div class="mica" style="top: 200px; left: 80px;"></div>
                    <div class="mica" style="top: 240px; left: 30px;"></div>
                    <div class="mica" style="top: 280px; left: 70px;"></div>
                    <div class="mica" style="top: 320px; left: 50px;"></div>
                    <div class="mica" style="top: 360px; left: 90px;"></div>
                    <div class="mica" style="top: 400px; left: 25px;"></div>
                    <div class="mica" style="top: 440px; left: 65px;"></div>
                    <div class="mica" style="top: 480px; left: 45px;"></div>
                    <div class="mica" style="top: 520px; left: 85px;"></div>
                </div>
                
                <!-- Scallop Shells -->
                <div class="shell" style="top: 100px; left: 45px;"></div>
                <div class="shell" style="top: 180px; left: 75px;"></div>
                <div class="shell" style="top: 260px; left: 35px;"></div>
                <div class="shell" style="top: 340px; left: 65px;"></div>
                <div class="shell" style="top: 420px; left: 55px;"></div>
                <div class="shell" style="top: 500px; left: 75px;"></div>
                
                <!-- Fine Sand Pockets -->
                <div class="sand-pocket" style="top: 140px; left: 15px; width: 40px; height: 30px;"></div>
                <div class="sand-pocket" style="top: 300px; left: 60px; width: 35px; height: 25px;"></div>
                <div class="sand-pocket" style="top: 460px; left: 20px; width: 45px; height: 35px;"></div>
            </div>
            
            <div class="description-panel">
                <h3>Layer 1: Silt (Saturated)</h3>
                
                <div class="property">
                    <div class="property-title">Soil Classification</div>
                    <div class="property-desc">Silt - Non-uniform, fully saturated marine deposit</div>
                </div>
                
                <div class="property">
                    <div class="property-title">Saturation State</div>
                    <div class="property-desc">Completely saturated below groundwater table</div>
                </div>
                
                <div class="property">
                    <div class="property-title">Soil Quality</div>
                    <div class="property-desc">Non-uniform distribution with varying density</div>
                </div>
                
                <div class="property">
                    <div class="property-title">Mineral Inclusions</div>
                    <div class="property-desc">Contains mica flakes - shiny mineral particles</div>
                </div>
                
                <div class="property">
                    <div class="property-title">Organic Content</div>
                    <div class="property-desc">Scallop shells scattered throughout layer</div>
                </div>
                
                <div class="property">
                    <div class="property-title">Local Features</div>
                    <div class="property-desc">Fine sand pockets in isolated areas</div>
                </div>
                
                <div class="property">
                    <div class="property-title">Shake Test Response</div>
                    <div class="property-desc">No dilatancy reaction observed</div>
                </div>
                
                <div class="property">
                    <div class="property-title">Dry Strength</div>
                    <div class="property-desc">Low resistance when dried</div>
                </div>
                
                <div class="property">
                    <div class="property-title">Toughness</div>
                    <div class="property-desc">Medium plasticity and workability</div>
                </div>
            </div>
        </div>
        
        <div class="legend">
            <h3>Stratigraphic Legend</h3>
            <div class="legend-item">
                <div class="legend-symbol" style="background: linear-gradient(to bottom, #8B7355, #A0826D);"></div>
                <span>Silt - Saturated, non-uniform marine deposit</span>
            </div>
            <div class="legend-item">
                <div class="legend-symbol" style="background: #FFD700; border-radius: 50%;"></div>
                <span>Mica inclusions - Shiny mineral flakes</span>
            </div>
            <div class="legend-item">
                <div class="legend-symbol" style="background: #F5F5DC; border: 2px solid #8B7355; border-radius: 50%;"></div>
                <span>Scallop shells - Organic marine fossils</span>
            </div>
            <div class="legend-item">
                <div class="legend-symbol" style="background: rgba(222, 184, 135, 0.7); border: 1px dashed #DEB887;"></div>
                <span>Local fine sand pockets</span>
            </div>
            <div class="legend-item">
                <div class="legend-symbol" style="background: #1e90ff;"></div>
                <span>Groundwater level</span>
            </div>
            <div class="legend-item">
                <div class="legend-symbol" style="background: linear-gradient(to bottom, #228B22, #32CD32);"></div>
                <span>Ground surface with vegetation</span>
            </div>
        </div>
        
        <div class="footer">
            Artistic Stratigraphic Profile | Visual Representation | Font: Arial
        </div>
    </div>
</body>
</html>
