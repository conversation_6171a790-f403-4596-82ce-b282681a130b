<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> and Scallop Illustration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 25%, #ffecd2 50%, #fcb69f 75%, #ff9a9e 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid transparent;
            border-image: linear-gradient(90deg, #ff9a9e, #fecfef, #ffecd2) 1;
        }
        h1 {
            font-size: 28px;
            font-weight: bold;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24, #ff9ff3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
            font-family: Arial, sans-serif;
        }
        .subtitle {
            font-size: 16px;
            color: #7f8c8d;
            font-style: italic;
            font-family: Arial, sans-serif;
        }
        .illustration-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin: 40px 0;
        }
        .specimen-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 2px solid #e0e0e0;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .specimen-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        .specimen-title {
            font-size: 22px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
            font-family: Arial, sans-serif;
        }
        .specimen-visual {
            width: 100%;
            height: 300px;
            border: 2px solid #bdc3c7;
            border-radius: 10px;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }
        .mica-visual {
            background: radial-gradient(circle at 30% 40%, #FFD700 0%, #FFA500 30%, #FF8C00 60%, #B8860B 100%);
            position: relative;
        }
        .mica-flake {
            position: absolute;
            background: linear-gradient(45deg, #FFD700, #FFA500, #FFD700);
            border-radius: 50% 20% 50% 20%;
            animation: shimmer 3s ease-in-out infinite;
            box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }
        @keyframes shimmer {
            0%, 100% { opacity: 0.8; transform: scale(1) rotate(0deg); }
            50% { opacity: 1; transform: scale(1.1) rotate(5deg); }
        }
        .scallop-visual {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #2F4F4F 100%);
            position: relative;
        }
        .scallop-shell {
            position: absolute;
            background: linear-gradient(45deg, #F5F5DC, #DDD, #F5F5DC);
            border-radius: 50% 50% 40% 40%;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            border: 2px solid #8B7355;
        }
        .scallop-ridges {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: repeating-linear-gradient(
                45deg,
                transparent 0px,
                rgba(139, 115, 85, 0.3) 2px,
                transparent 4px
            );
            border-radius: 50% 50% 40% 40%;
        }
        .specimen-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        .info-item {
            margin: 10px 0;
            font-size: 14px;
            font-family: Arial, sans-serif;
        }
        .info-label {
            font-weight: bold;
            color: #2c3e50;
        }
        .info-value {
            color: #7f8c8d;
        }
        .comparison-section {
            margin-top: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
        .comparison-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
            font-family: Arial, sans-serif;
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .comparison-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            font-family: Arial, sans-serif;
        }
        .comparison-header {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 16px;
        }
        .size-reference {
            margin-top: 30px;
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .size-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: center;
            font-family: Arial, sans-serif;
        }
        .size-scale {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 20px 0;
        }
        .scale-item {
            text-align: center;
            font-family: Arial, sans-serif;
        }
        .scale-visual {
            width: 60px;
            height: 60px;
            margin: 0 auto 10px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid transparent;
            border-image: linear-gradient(90deg, #ff9a9e, #fecfef) 1;
            font-size: 12px;
            color: #7f8c8d;
            text-align: center;
            font-family: Arial, sans-serif;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Mica and Scallop Specimens</h1>
            <div class="subtitle">Geological Inclusions in Silt Deposits</div>
        </div>
        
        <div class="illustration-section">
            <!-- Mica Specimen -->
            <div class="specimen-card">
                <div class="specimen-title">Mica Flakes</div>
                <div class="specimen-visual mica-visual">
                    <div class="mica-flake" style="top: 20%; left: 15%; width: 40px; height: 25px;"></div>
                    <div class="mica-flake" style="top: 45%; left: 60%; width: 35px; height: 20px; animation-delay: 1s;"></div>
                    <div class="mica-flake" style="top: 70%; left: 25%; width: 30px; height: 18px; animation-delay: 2s;"></div>
                    <div class="mica-flake" style="top: 30%; left: 75%; width: 25px; height: 15px; animation-delay: 0.5s;"></div>
                    <div class="mica-flake" style="top: 60%; left: 80%; width: 20px; height: 12px; animation-delay: 1.5s;"></div>
                </div>
                <div class="specimen-info">
                    <div class="info-item">
                        <span class="info-label">Mineral Type:</span>
                        <span class="info-value">Phyllosilicate</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Crystal System:</span>
                        <span class="info-value">Monoclinic</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Hardness:</span>
                        <span class="info-value">2-3 (Mohs scale)</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Color:</span>
                        <span class="info-value">Golden, silver, brown</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Luster:</span>
                        <span class="info-value">Metallic to pearly</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Cleavage:</span>
                        <span class="info-value">Perfect basal</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Size Range:</span>
                        <span class="info-value">0.1-5 mm</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Origin:</span>
                        <span class="info-value">Metamorphic/Igneous</span>
                    </div>
                </div>
            </div>
            
            <!-- Scallop Specimen -->
            <div class="specimen-card">
                <div class="specimen-title">Scallop Shells</div>
                <div class="specimen-visual scallop-visual">
                    <div class="scallop-shell" style="top: 15%; left: 20%; width: 80px; height: 70px;">
                        <div class="scallop-ridges"></div>
                    </div>
                    <div class="scallop-shell" style="top: 50%; left: 55%; width: 60px; height: 50px;">
                        <div class="scallop-ridges"></div>
                    </div>
                    <div class="scallop-shell" style="top: 25%; left: 70%; width: 45px; height: 40px;">
                        <div class="scallop-ridges"></div>
                    </div>
                    <div class="scallop-shell" style="top: 70%; left: 15%; width: 50px; height: 45px;">
                        <div class="scallop-ridges"></div>
                    </div>
                </div>
                <div class="specimen-info">
                    <div class="info-item">
                        <span class="info-label">Scientific Name:</span>
                        <span class="info-value">Pectinidae family</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Shell Type:</span>
                        <span class="info-value">Bivalve mollusk</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Composition:</span>
                        <span class="info-value">Calcium carbonate</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Structure:</span>
                        <span class="info-value">Aragonite + Calcite</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Shape:</span>
                        <span class="info-value">Fan-shaped, ribbed</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Color:</span>
                        <span class="info-value">White, cream, brown</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Size Range:</span>
                        <span class="info-value">5-150 mm</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Environment:</span>
                        <span class="info-value">Marine shallow water</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="comparison-section">
            <div class="comparison-title">Comparative Analysis</div>
            <div class="comparison-grid">
                <div class="comparison-item">
                    <div class="comparison-header">Property</div>
                </div>
                <div class="comparison-item">
                    <div class="comparison-header">Mica</div>
                </div>
                <div class="comparison-item">
                    <div class="comparison-header">Scallop Shell</div>
                </div>
                
                <div class="comparison-item">Origin</div>
                <div class="comparison-item">Mineral (Geological)</div>
                <div class="comparison-item">Biological (Marine)</div>
                
                <div class="comparison-item">Durability</div>
                <div class="comparison-item">High (Resistant)</div>
                <div class="comparison-item">Moderate (Dissolves)</div>
                
                <div class="comparison-item">Identification</div>
                <div class="comparison-item">Metallic luster, flaky</div>
                <div class="comparison-item">Ribbed pattern, curved</div>
                
                <div class="comparison-item">Significance</div>
                <div class="comparison-item">Indicates weathering</div>
                <div class="comparison-item">Indicates marine origin</div>
                
                <div class="comparison-item">Preservation</div>
                <div class="comparison-item">Excellent</div>
                <div class="comparison-item">Good in alkaline soils</div>
                
                <div class="comparison-item">Engineering Impact</div>
                <div class="comparison-item">Affects permeability</div>
                <div class="comparison-item">Creates weak planes</div>
            </div>
        </div>
        
        <div class="size-reference">
            <div class="size-title">Size Reference Scale</div>
            <div class="size-scale">
                <div class="scale-item">
                    <div class="scale-visual" style="background: #ff6b6b; width: 20px; height: 20px;">1</div>
                    <div>1 mm</div>
                    <div>Fine mica</div>
                </div>
                <div class="scale-item">
                    <div class="scale-visual" style="background: #feca57; width: 35px; height: 35px;">5</div>
                    <div>5 mm</div>
                    <div>Large mica</div>
                </div>
                <div class="scale-item">
                    <div class="scale-visual" style="background: #48dbfb; width: 50px; height: 50px;">20</div>
                    <div>20 mm</div>
                    <div>Small scallop</div>
                </div>
                <div class="scale-item">
                    <div class="scale-visual" style="background: #0abde3; width: 60px; height: 60px;">50</div>
                    <div>50 mm</div>
                    <div>Large scallop</div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            Scientific Illustration | Mica and Scallop Specimens | Font: Arial
        </div>
    </div>
</body>
</html>
