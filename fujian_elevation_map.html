<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>福建省高程矢量图</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }
        h1 {
            color: #2c3e50;
            font-size: 28px;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #7f8c8d;
            font-size: 16px;
        }
        .map-container {
            width: 100%;
            height: 800px;
            margin: 20px 0;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .info-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 30px;
        }
        .info-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        .info-box h3 {
            margin-top: 0;
            font-size: 18px;
        }
        .elevation-legend {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin: 8px 0;
        }
        .legend-color {
            width: 30px;
            height: 20px;
            margin-right: 10px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>福建省高程矢量图</h1>
            <div class="subtitle">Fujian Province Elevation Vector Map</div>
        </div>
        
        <div id="map" class="map-container"></div>
        
        <div class="info-panel">
            <div class="info-box">
                <h3>地理信息</h3>
                <p><strong>省份：</strong>福建省</p>
                <p><strong>面积：</strong>约12.14万平方公里</p>
                <p><strong>最高点：</strong>黄岗山 (2158米)</p>
                <p><strong>最低点：</strong>海平面 (0米)</p>
                <p><strong>地形特征：</strong>山地丘陵为主，沿海平原</p>
            </div>
            <div class="info-box">
                <h3>高程分布</h3>
                <p><strong>高山区：</strong>1500-2158米 (武夷山脉)</p>
                <p><strong>中山区：</strong>800-1500米 (戴云山脉)</p>
                <p><strong>低山区：</strong>200-800米 (丘陵地带)</p>
                <p><strong>平原区：</strong>0-200米 (沿海平原)</p>
                <p><strong>山地比例：</strong>约占全省面积的90%</p>
            </div>
        </div>
        
        <div class="elevation-legend">
            <h3>高程图例 (米)</h3>
            <div class="legend-item">
                <div class="legend-color" style="background: #8B4513;"></div>
                <span>2000-2158米 (极高山)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #A0522D;"></div>
                <span>1500-2000米 (高山)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #CD853F;"></div>
                <span>1000-1500米 (中高山)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #DEB887;"></div>
                <span>500-1000米 (低山)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #F0E68C;"></div>
                <span>200-500米 (丘陵)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #98FB98;"></div>
                <span>50-200米 (台地)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #87CEEB;"></div>
                <span>0-50米 (平原)</span>
            </div>
        </div>
    </div>

    <script>
        // 福建省边界坐标 (简化版本，基于真实地理数据)
        const fujianBoundary = {
            lon: [115.8, 116.2, 116.8, 117.5, 118.2, 118.8, 119.5, 120.2, 120.8, 121.2, 121.0, 120.5, 119.8, 119.2, 118.5, 117.8, 117.2, 116.5, 115.8, 115.8],
            lat: [28.2, 28.0, 27.8, 27.5, 27.2, 26.8, 26.5, 26.2, 25.8, 25.5, 24.8, 24.2, 23.8, 24.1, 24.5, 25.0, 25.5, 26.0, 26.5, 28.2]
        };

        // 主要山脉和高程点
        const elevationData = [
            // 武夷山脉 (西北部)
            {name: '黄岗山', lon: 117.7, lat: 27.9, elevation: 2158, type: 'peak'},
            {name: '武夷山', lon: 117.6, lat: 27.8, elevation: 2000, type: 'peak'},
            {name: '三清山', lon: 117.5, lat: 28.0, elevation: 1800, type: 'peak'},
            
            // 戴云山脉 (中部)
            {name: '戴云山', lon: 118.5, lat: 25.8, elevation: 1856, type: 'peak'},
            {name: '德化山', lon: 118.3, lat: 25.5, elevation: 1600, type: 'peak'},
            {name: '永春山', lon: 118.2, lat: 25.3, elevation: 1400, type: 'peak'},
            
            // 鹫峰山脉 (东南部)
            {name: '鹫峰山', lon: 119.2, lat: 26.2, elevation: 1200, type: 'peak'},
            {name: '太姥山', lon: 120.1, lat: 27.3, elevation: 917, type: 'peak'},
            
            // 沿海丘陵
            {name: '福州丘陵', lon: 119.3, lat: 26.1, elevation: 300, type: 'hill'},
            {name: '厦门丘陵', lon: 118.1, lat: 24.5, elevation: 200, type: 'hill'},
            {name: '泉州丘陵', lon: 118.6, lat: 24.9, elevation: 250, type: 'hill'},
            
            // 沿海平原
            {name: '福州平原', lon: 119.3, lat: 26.0, elevation: 20, type: 'plain'},
            {name: '泉州平原', lon: 118.6, lat: 24.8, elevation: 15, type: 'plain'},
            {name: '漳州平原', lon: 117.6, lat: 24.5, elevation: 25, type: 'plain'},
            {name: '莆田平原', lon: 119.0, lat: 25.4, elevation: 18, type: 'plain'}
        ];

        // 创建高程等值线数据
        function createElevationContours() {
            const contours = [];
            const elevationLevels = [0, 50, 200, 500, 1000, 1500, 2000];
            const colors = ['#87CEEB', '#98FB98', '#F0E68C', '#DEB887', '#CD853F', '#A0522D', '#8B4513'];
            
            elevationLevels.forEach((level, index) => {
                if (index < elevationLevels.length - 1) {
                    // 生成等值线区域
                    const contourData = generateContourForLevel(level, elevationLevels[index + 1]);
                    contours.push({
                        type: 'scatter',
                        mode: 'lines',
                        fill: 'toself',
                        x: contourData.lon,
                        y: contourData.lat,
                        fillcolor: colors[index],
                        line: {color: colors[index], width: 1},
                        name: `${level}-${elevationLevels[index + 1]}m`,
                        hovertemplate: `高程: ${level}-${elevationLevels[index + 1]}米<extra></extra>`
                    });
                }
            });
            
            return contours;
        }

        // 生成特定高程级别的等值线
        function generateContourForLevel(minElev, maxElev) {
            const centerLon = 118.5;
            const centerLat = 26.0;
            const points = 50;
            const lon = [];
            const lat = [];
            
            // 根据高程级别调整区域大小和位置
            let radiusLon, radiusLat, offsetLon, offsetLat;
            
            if (minElev >= 1500) {
                // 高山区 - 武夷山脉
                radiusLon = 0.8; radiusLat = 0.6;
                offsetLon = -0.8; offsetLat = 1.8;
            } else if (minElev >= 1000) {
                // 中山区 - 戴云山脉
                radiusLon = 1.2; radiusLat = 0.8;
                offsetLon = 0; offsetLat = -0.2;
            } else if (minElev >= 500) {
                // 低山区
                radiusLon = 1.8; radiusLat = 1.2;
                offsetLon = 0.2; offsetLat = 0.3;
            } else if (minElev >= 200) {
                // 丘陵区
                radiusLon = 2.2; radiusLat = 1.6;
                offsetLon = 0.5; offsetLat = 0;
            } else if (minElev >= 50) {
                // 台地
                radiusLon = 2.5; radiusLat = 1.8;
                offsetLon = 0.8; offsetLat = -0.3;
            } else {
                // 平原区 - 沿海
                radiusLon = 2.8; radiusLat = 2.0;
                offsetLon = 1.0; offsetLat = -0.5;
            }
            
            for (let i = 0; i <= points; i++) {
                const angle = (i / points) * 2 * Math.PI;
                const noise = 0.1 * Math.sin(angle * 5) * Math.random();
                lon.push(centerLon + offsetLon + (radiusLon + noise) * Math.cos(angle));
                lat.push(centerLat + offsetLat + (radiusLat + noise) * Math.sin(angle));
            }
            
            return {lon, lat};
        }

        // 创建主要城市标注
        const cities = [
            {name: '福州', lon: 119.3, lat: 26.1, population: '774万'},
            {name: '厦门', lon: 118.1, lat: 24.5, population: '516万'},
            {name: '泉州', lon: 118.6, lat: 24.9, population: '878万'},
            {name: '漳州', lon: 117.6, lat: 24.5, population: '507万'},
            {name: '莆田', lon: 119.0, lat: 25.4, population: '321万'},
            {name: '三明', lon: 117.6, lat: 26.3, population: '248万'},
            {name: '龙岩', lon: 117.0, lat: 25.1, population: '271万'},
            {name: '南平', lon: 118.2, lat: 26.6, population: '268万'},
            {name: '宁德', lon: 119.5, lat: 26.7, population: '290万'}
        ];

        // 创建图表数据
        const traces = [];
        
        // 添加高程等值线
        traces.push(...createElevationContours());
        
        // 添加省界
        traces.push({
            type: 'scatter',
            mode: 'lines',
            x: fujianBoundary.lon,
            y: fujianBoundary.lat,
            line: {color: '#2c3e50', width: 3},
            name: '省界',
            hoverinfo: 'skip'
        });
        
        // 添加高程点
        const peaks = elevationData.filter(d => d.type === 'peak');
        const hills = elevationData.filter(d => d.type === 'hill');
        const plains = elevationData.filter(d => d.type === 'plain');
        
        traces.push({
            type: 'scatter',
            mode: 'markers+text',
            x: peaks.map(d => d.lon),
            y: peaks.map(d => d.lat),
            text: peaks.map(d => d.name),
            textposition: 'top center',
            marker: {
                size: peaks.map(d => Math.sqrt(d.elevation) / 5),
                color: peaks.map(d => d.elevation),
                colorscale: 'Reds',
                symbol: 'triangle-up',
                line: {color: 'white', width: 2}
            },
            name: '山峰',
            hovertemplate: '<b>%{text}</b><br>高程: %{marker.color}米<extra></extra>'
        });
        
        // 添加城市标注
        traces.push({
            type: 'scatter',
            mode: 'markers+text',
            x: cities.map(d => d.lon),
            y: cities.map(d => d.lat),
            text: cities.map(d => d.name),
            textposition: 'bottom center',
            marker: {
                size: 12,
                color: '#e74c3c',
                symbol: 'circle',
                line: {color: 'white', width: 2}
            },
            name: '主要城市',
            hovertemplate: '<b>%{text}</b><br>人口: %{customdata}<extra></extra>',
            customdata: cities.map(d => d.population)
        });

        // 图表布局
        const layout = {
            title: {
                text: '福建省高程分布图',
                font: {size: 20, color: '#2c3e50'}
            },
            xaxis: {
                title: '经度 (°E)',
                range: [115.5, 121.5],
                showgrid: true,
                gridcolor: 'rgba(0,0,0,0.1)'
            },
            yaxis: {
                title: '纬度 (°N)',
                range: [23.5, 28.5],
                showgrid: true,
                gridcolor: 'rgba(0,0,0,0.1)',
                scaleanchor: 'x',
                scaleratio: 1
            },
            showlegend: true,
            legend: {
                x: 0.02,
                y: 0.98,
                bgcolor: 'rgba(255,255,255,0.8)',
                bordercolor: '#bdc3c7',
                borderwidth: 1
            },
            plot_bgcolor: 'white',
            paper_bgcolor: 'rgba(255,255,255,0.9)',
            font: {family: 'Arial', size: 12},
            hovermode: 'closest'
        };

        // 配置选项
        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToAdd: ['downloadPlot'],
            toImageButtonOptions: {
                format: 'png',
                filename: 'fujian_elevation_map',
                height: 800,
                width: 1200,
                scale: 2
            }
        };

        // 绘制图表
        Plotly.newPlot('map', traces, layout, config);
    </script>
</body>
</html>
