<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Beautiful Stratigraphic Profile</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 25%, #ffecd2 50%, #fcb69f 75%, #ff9a9e 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid transparent;
            border-image: linear-gradient(90deg, #ff9a9e, #fecfef, #ffecd2) 1;
        }
        h1 {
            font-size: 28px;
            font-weight: bold;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24, #ff9ff3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
            font-family: Arial, sans-serif;
        }
        .subtitle {
            font-size: 16px;
            color: #7f8c8d;
            font-style: italic;
            font-family: Arial, sans-serif;
        }
        .profile-container {
            width: 100%;
            height: 900px;
            margin: 30px 0;
            background: linear-gradient(135deg,
                rgba(255, 154, 158, 0.08) 0%,
                rgba(254, 207, 239, 0.08) 25%,
                rgba(255, 236, 210, 0.08) 50%,
                rgba(252, 182, 159, 0.08) 75%,
                rgba(255, 183, 197, 0.08) 100%);
            border-radius: 15px;
            padding: 20px;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.05);
            border: 1px solid rgba(255, 154, 158, 0.2);
        }
        .description-section {
            margin-top: 40px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        .layer-info {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
        }
        .properties-info {
            background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(254, 202, 87, 0.3);
        }
        .info-box h3 {
            margin-top: 0;
            font-size: 18px;
            font-family: Arial, sans-serif;
        }
        .info-item {
            margin: 12px 0;
            font-size: 14px;
            font-family: Arial, sans-serif;
        }
        .legend {
            margin-top: 30px;
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .legend h3 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 18px;
            font-family: Arial, sans-serif;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin: 15px 0;
            font-family: Arial, sans-serif;
            font-size: 14px;
        }
        .legend-symbol {
            width: 35px;
            height: 25px;
            margin-right: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid transparent;
            border-image: linear-gradient(90deg, #ff9a9e, #fecfef) 1;
            font-size: 12px;
            color: #7f8c8d;
            text-align: center;
            font-family: Arial, sans-serif;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Stratigraphic Profile - Layer 1</h1>
            <div class="subtitle">Saturated Silt with Natural Inclusions</div>
        </div>

        <div id="profile" class="profile-container"></div>

        <div class="description-section">
            <div class="layer-info">
                <h3>Layer Characteristics</h3>
                <div class="info-item"><strong>Soil Type:</strong> Silt (ML)</div>
                <div class="info-item"><strong>Saturation:</strong> Fully saturated</div>
                <div class="info-item"><strong>Uniformity:</strong> Non-uniform distribution</div>
                <div class="info-item"><strong>Mineral Content:</strong> Contains mica flakes</div>
                <div class="info-item"><strong>Organic Matter:</strong> Scallop shell fragments</div>
                <div class="info-item"><strong>Local Features:</strong> Fine sand pockets</div>
            </div>

            <div class="properties-info">
                <h3>Engineering Properties</h3>
                <div class="info-item"><strong>Shake Test:</strong> No dilatancy reaction</div>
                <div class="info-item"><strong>Dry Strength:</strong> Low resistance</div>
                <div class="info-item"><strong>Toughness:</strong> Medium plasticity</div>
                <div class="info-item"><strong>Permeability:</strong> Low to moderate</div>
                <div class="info-item"><strong>Compressibility:</strong> Medium to high</div>
                <div class="info-item"><strong>Bearing Capacity:</strong> Low to moderate</div>
            </div>
        </div>

        <div class="legend">
            <h3>Visual Legend</h3>
            <div class="legend-item">
                <div class="legend-symbol" style="background: linear-gradient(to bottom, #8B7355, #A0826D);"></div>
                <span>Silt Layer - Saturated, non-uniform marine deposit</span>
            </div>
            <div class="legend-item">
                <div class="legend-symbol" style="background: #FFD700; border-radius: 50%;"></div>
                <span>Mica Inclusions - Shiny mineral particles</span>
            </div>
            <div class="legend-item">
                <div class="legend-symbol" style="background: #F5F5DC; border: 2px solid #8B7355;"></div>
                <span>Scallop Shells - Organic marine fossils</span>
            </div>
            <div class="legend-item">
                <div class="legend-symbol" style="background: rgba(222, 184, 135, 0.8);"></div>
                <span>Fine Sand Pockets - Local sandy inclusions</span>
            </div>
            <div class="legend-item">
                <div class="legend-symbol" style="background: linear-gradient(90deg, #1e90ff, #00bfff);"></div>
                <span>Groundwater Level - Saturation boundary</span>
            </div>
        </div>

        <div class="footer">
            Beautiful Stratigraphic Profile | Visual Representation without Grid | Font: Arial
        </div>
    </div>

    <script>
        // Create beautiful stratigraphic profile without grid and numbers
        function createBeautifulProfile() {
            const traces = [];

            // Ground surface with natural curve - narrower width
            const surfaceX = [];
            const surfaceY = [];
            for (let i = 0; i <= 60; i++) {
                surfaceX.push(i * 0.1);
                surfaceY.push(Math.sin(i * 0.1) * 0.05);
            }

            traces.push({
                x: surfaceX,
                y: surfaceY,
                mode: 'lines',
                fill: 'tonexty',
                fillcolor: 'rgba(34, 139, 34, 0.8)',
                line: {color: '#2d5016', width: 4},
                name: 'Ground Surface',
                hoverinfo: 'skip'
            });

            // Silt layer with organic texture - narrower and taller
            traces.push({
                x: [0, 6, 6, 0, 0],
                y: [0, 0, -10, -10, 0],
                fill: 'toself',
                fillcolor: 'rgba(139, 115, 85, 0.9)',
                line: {color: 'rgba(101, 67, 33, 0.8)', width: 3},
                name: 'Silt Layer (Saturated)',
                hovertemplate: '<b>Silt Layer</b><br>Saturated, non-uniform<br>Contains mica & scallop shells<extra></extra>'
            });

            // Add texture patterns with multiple overlays
            const texturePatterns = [
                {color: 'rgba(160, 130, 109, 0.6)', offset: 0.2},
                {color: 'rgba(101, 67, 33, 0.4)', offset: 0.5},
                {color: 'rgba(139, 115, 85, 0.5)', offset: 0.8}
            ];

            texturePatterns.forEach((pattern, index) => {
                traces.push({
                    x: [0, 6, 6, 0, 0],
                    y: [-pattern.offset, -pattern.offset, -10, -10, -pattern.offset],
                    fill: 'toself',
                    fillcolor: pattern.color,
                    line: {color: 'transparent'},
                    showlegend: false,
                    hoverinfo: 'skip'
                });
            });

            // Realistic mica distribution
            const micaCount = 40;
            const micaX = [];
            const micaY = [];
            const micaSizes = [];

            for (let i = 0; i < micaCount; i++) {
                micaX.push(0.3 + Math.random() * 5.4);
                micaY.push(-0.3 - Math.random() * 9.5);
                micaSizes.push(4 + Math.random() * 6);
            }

            traces.push({
                x: micaX,
                y: micaY,
                mode: 'markers',
                marker: {
                    size: micaSizes,
                    color: '#FFD700',
                    symbol: 'diamond',
                    line: {color: '#B8860B', width: 1},
                    opacity: 0.9
                },
                name: 'Mica Inclusions',
                hovertemplate: '<b>Mica</b><br>Mineral inclusion<extra></extra>'
            });

            // Scallop shells with natural distribution
            const shellCount = 12;
            const shellX = [];
            const shellY = [];
            const shellSizes = [];

            for (let i = 0; i < shellCount; i++) {
                shellX.push(0.5 + Math.random() * 5);
                shellY.push(-0.8 - Math.random() * 8.5);
                shellSizes.push(10 + Math.random() * 8);
            }

            traces.push({
                x: shellX,
                y: shellY,
                mode: 'markers',
                marker: {
                    size: shellSizes,
                    color: '#F5F5DC',
                    symbol: 'square',
                    line: {color: '#8B7355', width: 2},
                    opacity: 0.9
                },
                name: 'Scallop Shells',
                hovertemplate: '<b>Scallop Shell</b><br>Organic inclusion<extra></extra>'
            });

            // Fine sand pockets with organic shapes - adjusted for narrower profile
            const sandPockets = [
                {x: [4.5, 5.8, 5.6, 4.3, 4.5], y: [-1.5, -1.6, -3.0, -2.9, -1.5]},
                {x: [1.2, 2.8, 2.6, 1.0, 1.2], y: [-5.0, -5.1, -7.2, -7.1, -5.0]},
                {x: [3.0, 4.2, 4.0, 2.8, 3.0], y: [-2.5, -2.6, -4.0, -3.9, -2.5]}
            ];

            sandPockets.forEach((pocket, index) => {
                traces.push({
                    x: pocket.x,
                    y: pocket.y,
                    fill: 'toself',
                    fillcolor: 'rgba(222, 184, 135, 0.8)',
                    line: {color: '#DEB887', width: 2},
                    name: index === 0 ? 'Fine Sand Pockets' : '',
                    showlegend: index === 0,
                    hovertemplate: '<b>Fine Sand Pocket</b><br>Local sandy inclusion<extra></extra>'
                });
            });

            // Groundwater level with natural variation - adjusted for narrower profile
            const gwX = [];
            const gwY = [];
            for (let i = 0; i <= 60; i++) {
                gwX.push(i * 0.1);
                gwY.push(-0.4 + Math.sin(i * 0.3) * 0.08);
            }

            traces.push({
                x: gwX,
                y: gwY,
                mode: 'lines',
                line: {color: '#1e90ff', width: 5},
                name: 'Groundwater Level',
                hovertemplate: '<b>Groundwater Level</b><br>Saturation boundary<extra></extra>'
            });

            return traces;
        }

        const traces = createBeautifulProfile();

        const layout = {
            title: {
                text: '',
                font: {
                    family: 'Arial',
                    size: 18,
                    color: '#2c3e50'
                }
            },
            xaxis: {
                title: '',
                showgrid: false,
                showticklabels: false,
                zeroline: false,
                range: [0, 6]
            },
            yaxis: {
                title: '',
                showgrid: false,
                showticklabels: false,
                zeroline: false,
                range: [-11, 1],
                scaleanchor: 'x',
                scaleratio: 2
            },
            plot_bgcolor: 'rgba(255, 255, 255, 0)',
            paper_bgcolor: 'rgba(255, 255, 255, 0)',
            font: {
                family: 'Arial',
                size: 12,
                color: '#2c3e50'
            },
            legend: {
                x: 0.02,
                y: 0.98,
                bgcolor: 'rgba(255,255,255,0.9)',
                bordercolor: 'rgba(255, 107, 107, 0.5)',
                borderwidth: 2,
                font: {
                    family: 'Arial',
                    size: 12,
                    color: '#2c3e50'
                }
            },
            margin: {l: 20, r: 20, t: 20, b: 20},
            hovermode: 'closest'
        };

        const config = {
            responsive: true,
            displayModeBar: false,
            staticPlot: false
        };

        Plotly.newPlot('profile', traces, layout, config);
    </script>
</body>
</html>
