<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Particle Size Distribution Curves Comparison</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 25%, #ffecd2 50%, #fcb69f 75%, #ff9a9e 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: linear-gradient(145deg, rgba(255,255,255,0.95) 0%, rgba(248,249,250,0.9) 100%);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid transparent;
            border-image: linear-gradient(90deg, #ff9a9e, #fecfef, #ffecd2) 1;
        }
        h1 {
            font-size: 28px;
            font-weight: bold;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24, #ff9ff3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
            font-family: Arial, sans-serif;
        }
        .subtitle {
            font-size: 16px;
            color: #7f8c8d;
            font-style: italic;
            font-family: Arial, sans-serif;
        }
        .chart-container {
            width: 100%;
            height: 700px;
            margin: 30px 0;
            background: linear-gradient(135deg, 
                rgba(255, 154, 158, 0.08) 0%, 
                rgba(254, 207, 239, 0.08) 25%,
                rgba(255, 236, 210, 0.08) 50%,
                rgba(252, 182, 159, 0.08) 75%,
                rgba(255, 183, 197, 0.08) 100%);
            border-radius: 15px;
            padding: 20px;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.05);
            border: 1px solid rgba(255, 154, 158, 0.2);
        }
        .comparison-section {
            margin-top: 40px;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }
        .sample-box {
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            color: white;
        }
        .sample1 {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }
        .sample2 {
            background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
        }
        .median-box {
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
        }
        .sample-box h3 {
            margin-top: 0;
            font-size: 18px;
            font-family: Arial, sans-serif;
        }
        .param-item {
            margin: 8px 0;
            font-size: 14px;
            font-family: Arial, sans-serif;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 11px;
            margin-top: 20px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            font-family: Arial, sans-serif;
        }
        .data-table th, .data-table td {
            border: 1px solid #e0e0e0;
            padding: 8px;
            text-align: center;
        }
        .data-table th {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            font-weight: bold;
        }
        .data-table td {
            background: rgba(255,255,255,0.8);
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid transparent;
            border-image: linear-gradient(90deg, #ff9a9e, #fecfef) 1;
            font-size: 12px;
            color: #7f8c8d;
            text-align: center;
            font-family: Arial, sans-serif;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Particle Size Distribution Curves Comparison</h1>
            <div class="subtitle">Two Sample Analysis with Median Diameter Calculation</div>
        </div>
        
        <div id="chart" class="chart-container"></div>
        
        <div class="comparison-section">
            <div class="sample-box sample1">
                <h3>Sample 1</h3>
                <div class="param-item"><strong>D₁₀:</strong> 0.18 mm</div>
                <div class="param-item"><strong>D₃₀:</strong> 0.32 mm</div>
                <div class="param-item"><strong>D₅₀:</strong> 0.42 mm</div>
                <div class="param-item"><strong>D₆₀:</strong> 0.48 mm</div>
                <div class="param-item"><strong>Cᵤ:</strong> 2.67</div>
                <div class="param-item"><strong>Cᶜ:</strong> 1.18</div>
                <div class="param-item"><strong>Gravel:</strong> 1.72%</div>
                <div class="param-item"><strong>Sand:</strong> 97.85%</div>
                <div class="param-item"><strong>Fines:</strong> 0.43%</div>
            </div>
            
            <div class="sample-box sample2">
                <h3>Sample 2</h3>
                <div class="param-item"><strong>D₁₀:</strong> 0.16 mm</div>
                <div class="param-item"><strong>D₃₀:</strong> 0.28 mm</div>
                <div class="param-item"><strong>D₅₀:</strong> 0.38 mm</div>
                <div class="param-item"><strong>D₆₀:</strong> 0.45 mm</div>
                <div class="param-item"><strong>Cᵤ:</strong> 2.81</div>
                <div class="param-item"><strong>Cᶜ:</strong> 1.09</div>
                <div class="param-item"><strong>Gravel:</strong> 5.69%</div>
                <div class="param-item"><strong>Sand:</strong> 93.83%</div>
                <div class="param-item"><strong>Fines:</strong> 0.48%</div>
            </div>
            
            <div class="sample-box median-box">
                <h3>Median Diameter (D₅₀)</h3>
                <div class="param-item"><strong>Sample 1 D₅₀:</strong> 0.42 mm</div>
                <div class="param-item"><strong>Sample 2 D₅₀:</strong> 0.38 mm</div>
                <div class="param-item"><strong>Difference:</strong> 0.04 mm</div>
                <div class="param-item"><strong>Classification:</strong> Medium Sand</div>
                <div class="param-item"><strong>Both samples:</strong> Well-graded</div>
                <div class="param-item"><strong>Similarity:</strong> Very High</div>
            </div>
        </div>
        
        <table class="data-table">
            <thead>
                <tr>
                    <th rowspan="2">Sieve Size (mm)</th>
                    <th colspan="2">Sample 1</th>
                    <th colspan="2">Sample 2</th>
                    <th rowspan="2">Difference (%)</th>
                </tr>
                <tr>
                    <th>Retained (%)</th>
                    <th>Passing (%)</th>
                    <th>Retained (%)</th>
                    <th>Passing (%)</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>&gt;2.0</td>
                    <td>0.40</td>
                    <td>99.60</td>
                    <td>0.137</td>
                    <td>99.863</td>
                    <td>+0.263</td>
                </tr>
                <tr>
                    <td>2.0-1.0</td>
                    <td>1.32</td>
                    <td>98.28</td>
                    <td>5.55</td>
                    <td>94.313</td>
                    <td>-3.967</td>
                </tr>
                <tr>
                    <td>1.0-0.5</td>
                    <td>27.90</td>
                    <td>70.38</td>
                    <td>32.48</td>
                    <td>61.833</td>
                    <td>-8.547</td>
                </tr>
                <tr>
                    <td>0.5-0.25</td>
                    <td>30.18</td>
                    <td>40.20</td>
                    <td>32.19</td>
                    <td>29.643</td>
                    <td>-10.557</td>
                </tr>
                <tr>
                    <td>0.25-0.125</td>
                    <td>29.17</td>
                    <td>11.03</td>
                    <td>21.61</td>
                    <td>8.033</td>
                    <td>-2.997</td>
                </tr>
                <tr>
                    <td>0.125-0.075</td>
                    <td>10.64</td>
                    <td>0.39</td>
                    <td>7.55</td>
                    <td>0.483</td>
                    <td>+0.093</td>
                </tr>
                <tr>
                    <td>&lt;0.063</td>
                    <td>0.43</td>
                    <td>-0.04</td>
                    <td>0.48</td>
                    <td>0.003</td>
                    <td>+0.043</td>
                </tr>
            </tbody>
        </table>
        
        <div class="footer">
            Comparison Analysis | Median Diameter (D₅₀) Calculation | Standards: ASTM D422, ASTM D6913
        </div>
    </div>

    <script>
        // Sample 1 data
        const particleSizes = [10, 2.0, 1.0, 0.5, 0.25, 0.125, 0.075, 0.063, 0.01];
        
        // Sample 1 cumulative passing
        const sample1Passing = [100, 99.60, 98.28, 70.38, 40.20, 11.03, 0.39, 0, 0];
        
        // Sample 2 cumulative passing
        const sample2Passing = [100, 99.863, 94.313, 61.833, 29.643, 8.033, 0.483, 0.003, 0];
        
        // Sample 1 curve
        const sample1Trace = {
            x: particleSizes,
            y: sample1Passing,
            mode: 'lines+markers+text',
            name: 'Sample 1',
            line: {
                color: '#ff6b6b',
                width: 4,
                shape: 'linear'
            },
            marker: {
                size: 10,
                color: '#ff6b6b',
                symbol: 'circle',
                line: {
                    color: 'white',
                    width: 2
                }
            },
            text: sample1Passing.map((value, i) => {
                const keyIndices = [1, 2, 3, 4, 5, 6];
                return keyIndices.includes(i) ? `${value.toFixed(1)}%` : '';
            }),
            textposition: 'top center',
            textfont: {
                size: 10,
                color: '#ff6b6b',
                family: 'Arial',
                style: 'bold'
            },
            hovertemplate: '<b>Sample 1</b><br>Size: %{x} mm<br>Passing: %{y:.2f}%<extra></extra>'
        };
        
        // Sample 2 curve
        const sample2Trace = {
            x: particleSizes,
            y: sample2Passing,
            mode: 'lines+markers+text',
            name: 'Sample 2',
            line: {
                color: '#feca57',
                width: 4,
                shape: 'linear'
            },
            marker: {
                size: 10,
                color: '#feca57',
                symbol: 'square',
                line: {
                    color: 'white',
                    width: 2
                }
            },
            text: sample2Passing.map((value, i) => {
                const keyIndices = [1, 2, 3, 4, 5, 6];
                return keyIndices.includes(i) ? `${value.toFixed(1)}%` : '';
            }),
            textposition: 'bottom center',
            textfont: {
                size: 10,
                color: '#feca57',
                family: 'Arial',
                style: 'bold'
            },
            hovertemplate: '<b>Sample 2</b><br>Size: %{x} mm<br>Passing: %{y:.3f}%<extra></extra>'
        };
        
        // Median diameter markers (D50)
        const medianTrace = {
            x: [0.42, 0.38],
            y: [50, 50],
            mode: 'markers+text',
            text: ['D₅₀=0.42mm (S1)', 'D₅₀=0.38mm (S2)'],
            textposition: ['top right', 'bottom right'],
            textfont: {
                size: 12,
                color: '#4ecdc4',
                family: 'Arial',
                style: 'bold'
            },
            marker: {
                size: 12,
                color: '#4ecdc4',
                symbol: 'diamond',
                line: {
                    color: 'white',
                    width: 2
                }
            },
            showlegend: false,
            name: 'Median Diameter (D₅₀)',
            hovertemplate: '<b>%{text}</b><br>Median Diameter<extra></extra>'
        };
        
        // 50% passing line
        const fiftyPercentLine = {
            x: [0.01, 10],
            y: [50, 50],
            mode: 'lines',
            line: {
                color: '#4ecdc4',
                width: 2,
                dash: 'dash'
            },
            name: '50% Passing Line',
            hoverinfo: 'skip'
        };
        
        const data = [sample1Trace, sample2Trace, medianTrace, fiftyPercentLine];
        
        const layout = {
            title: {
                text: 'Particle Size Distribution Curves Comparison',
                font: {
                    family: 'Arial',
                    size: 18,
                    color: '#2c3e50'
                }
            },
            xaxis: {
                title: {
                    text: 'Particle Size (mm)',
                    font: {
                        family: 'Arial',
                        size: 16,
                        color: '#2c3e50'
                    }
                },
                type: 'log',
                range: [Math.log10(0.01), Math.log10(10)],
                tickvals: [0.01, 0.063, 0.075, 0.125, 0.25, 0.5, 1.0, 2.0, 10],
                ticktext: ['0.01', '0.063', '0.075', '0.125', '0.25', '0.5', '1.0', '2.0', '10'],
                gridcolor: 'rgba(255, 107, 107, 0.3)',
                gridwidth: 2,
                tickfont: {
                    family: 'Arial',
                    size: 12,
                    color: '#2c3e50'
                }
            },
            yaxis: {
                title: {
                    text: 'Percentage Passing (%)',
                    font: {
                        family: 'Arial',
                        size: 16,
                        color: '#2c3e50'
                    }
                },
                range: [0, 100],
                dtick: 10,
                gridcolor: 'rgba(254, 202, 87, 0.3)',
                gridwidth: 2,
                tickfont: {
                    family: 'Arial',
                    size: 12,
                    color: '#2c3e50'
                }
            },
            plot_bgcolor: 'rgba(255, 255, 255, 0.8)',
            paper_bgcolor: 'rgba(255, 255, 255, 0.9)',
            font: {
                family: 'Arial',
                size: 12,
                color: '#2c3e50'
            },
            legend: {
                x: 0.02,
                y: 0.98,
                bgcolor: 'rgba(255,255,255,0.9)',
                bordercolor: 'rgba(255, 107, 107, 0.5)',
                borderwidth: 2,
                font: {
                    family: 'Arial',
                    size: 12,
                    color: '#2c3e50'
                }
            },
            shapes: [
                // Soil classification boundaries
                {
                    type: 'line',
                    x0: 0.075, x1: 0.075,
                    y0: 0, y1: 100,
                    line: {
                        color: '#ff6b6b',
                        width: 2,
                        dash: 'dot'
                    }
                },
                {
                    type: 'line',
                    x0: 2.0, x1: 2.0,
                    y0: 0, y1: 100,
                    line: {
                        color: '#ff6b6b',
                        width: 2,
                        dash: 'dot'
                    }
                }
            ],
            annotations: [
                {
                    x: Math.log10(0.03),
                    y: 5,
                    text: 'Fines',
                    showarrow: false,
                    font: {
                        family: 'Arial',
                        size: 10,
                        color: 'white'
                    },
                    bgcolor: 'rgba(255, 107, 107, 0.8)',
                    borderradius: 5
                },
                {
                    x: Math.log10(0.5),
                    y: 5,
                    text: 'Sand',
                    showarrow: false,
                    font: {
                        family: 'Arial',
                        size: 10,
                        color: 'white'
                    },
                    bgcolor: 'rgba(254, 202, 87, 0.8)',
                    borderradius: 5
                },
                {
                    x: Math.log10(5),
                    y: 5,
                    text: 'Gravel',
                    showarrow: false,
                    font: {
                        family: 'Arial',
                        size: 10,
                        color: 'white'
                    },
                    bgcolor: 'rgba(255, 159, 243, 0.8)',
                    borderradius: 5
                }
            ]
        };
        
        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToAdd: ['downloadPlot'],
            toImageButtonOptions: {
                format: 'png',
                filename: 'comparison_particle_size_distribution',
                height: 700,
                width: 1200,
                scale: 2
            }
        };
        
        Plotly.newPlot('chart', data, layout, config);
    </script>
</body>
</html>
