import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt

# Set font to Arial
plt.rcParams['font.family'] = 'Arial'
plt.rcParams['font.size'] = 12

print("Starting to generate particle size distribution curve...")

# Data from the table - simplified approach
# Particle sizes (mm) and corresponding cumulative passing percentages
particle_sizes = [100, 20.0, 2.00, 0.50, 0.25, 0.075, 0.01]
cumulative_mean = [100, 77.2, 40.9, 26.4, 21.6, 0, 0]  # Calculated from your data
cumulative_max = [100, 87.5, 44.3, 27.0, 20.7, 0, 0]   # Range envelope
cumulative_min = [100, 61.5, 32.2, 10.0, 6.4, 0, 0]    # Range envelope

# Create the plot
fig, ax = plt.subplots(figsize=(12, 8))

# Plot the curves with beautiful colors
ax.semilogx(particle_sizes, cumulative_mean,
           'o-', linewidth=3, markersize=8, color='#2E86AB',
           label='Average', markerfacecolor='white', markeredgewidth=2)

ax.fill_between(particle_sizes, cumulative_min, cumulative_max,
               alpha=0.3, color='#A23B72', label='Range (Min-Max)')

ax.semilogx(particle_sizes, cumulative_max,
           '--', linewidth=2, color='#F18F01', alpha=0.8, label='Maximum')

ax.semilogx(particle_sizes, cumulative_min,
           '--', linewidth=2, color='#C73E1D', alpha=0.8, label='Minimum')

# Customize the plot
ax.set_xlabel('Particle Size (mm)', fontsize=14, fontweight='bold')
ax.set_ylabel('Percentage Passing (%)', fontsize=14, fontweight='bold')
ax.set_title('Particle Size Distribution Curve\nSilty Sand',
             fontsize=16, fontweight='bold', pad=20)

# Set grid
ax.grid(True, which="both", ls="-", alpha=0.3, color='gray')
ax.grid(True, which="minor", ls=":", alpha=0.2, color='gray')

# Set axis limits and ticks
ax.set_xlim(0.01, 100)
ax.set_ylim(0, 100)

# Custom x-axis ticks for standard sieve sizes
sieve_sizes = [0.075, 0.15, 0.3, 0.6, 1.18, 2.36, 4.75, 9.5, 19, 37.5, 75]
ax.set_xticks(sieve_sizes)
ax.set_xticklabels([f'{size:.3f}' if size < 1 else f'{size:.1f}' for size in sieve_sizes])

# Y-axis ticks
ax.set_yticks(range(0, 101, 10))

# Add legend
ax.legend(loc='center right', frameon=True, fancybox=True, shadow=True,
         fontsize=12, framealpha=0.9)

# Add standard soil classification lines
ax.axvline(x=0.075, color='red', linestyle=':', alpha=0.7, linewidth=2)
ax.axvline(x=2.0, color='red', linestyle=':', alpha=0.7, linewidth=2)
ax.axvline(x=20.0, color='red', linestyle=':', alpha=0.7, linewidth=2)

# Add text annotations for soil fractions
ax.text(0.03, 95, 'Clay & Silt\n(<0.075mm)', ha='center', va='top',
        fontsize=10, bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.7))
ax.text(0.5, 95, 'Sand\n(0.075-2mm)', ha='center', va='top',
        fontsize=10, bbox=dict(boxstyle="round,pad=0.3", facecolor='lightyellow', alpha=0.7))
ax.text(8, 95, 'Gravel\n(2-20mm)', ha='center', va='top',
        fontsize=10, bbox=dict(boxstyle="round,pad=0.3", facecolor='lightcoral', alpha=0.7))
ax.text(50, 95, 'Cobbles\n(>20mm)', ha='center', va='top',
        fontsize=10, bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgreen', alpha=0.7))

# Improve layout
plt.tight_layout()

# Save the plot
plt.savefig('particle_size_distribution.png', dpi=300, bbox_inches='tight',
           facecolor='white', edgecolor='none')
plt.savefig('particle_size_distribution.pdf', bbox_inches='tight',
           facecolor='white', edgecolor='none')

# Don't show the plot in headless mode
# plt.show()

print("Particle size distribution curve has been generated!")
print("Files saved: particle_size_distribution.png and particle_size_distribution.pdf")
