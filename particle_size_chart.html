<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Particle Size Distribution Curve</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1), 0 0 0 1px rgba(255,255,255,0.5);
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 30px;
            font-size: 28px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .chart-container {
            width: 100%;
            height: 600px;
            margin: 20px 0;
            background: linear-gradient(145deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            padding: 15px;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.06);
        }
        .data-table {
            margin-top: 30px;
            width: 100%;
            border-collapse: collapse;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }
        .data-table th, .data-table td {
            border: 1px solid #e0e0e0;
            padding: 12px;
            text-align: center;
        }
        .data-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
        }
        .data-table td {
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
        }
        .data-table tr:nth-child(even) td {
            background: linear-gradient(145deg, #f8f9fa 0%, #e9ecef 100%);
        }
        .data-table tr:hover td {
            background: linear-gradient(145deg, #e3f2fd 0%, #bbdefb 100%);
            transform: scale(1.01);
            transition: all 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Particle Size Distribution Curve - Silty Sand</h1>

        <div id="chart" class="chart-container"></div>

        <table class="data-table">
            <thead>
                <tr>
                    <th rowspan="2">Soil Layer</th>
                    <th rowspan="2">Index Type</th>
                    <th colspan="6">Particle Size Composition (%)</th>
                </tr>
                <tr>
                    <th>&gt;20.0</th>
                    <th>20.0~2.00</th>
                    <th>2.00~0.50</th>
                    <th>0.50~0.25</th>
                    <th>0.25~0.075</th>
                    <th>&lt;0.075</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td rowspan="4">Silty Sand</td>
                    <td>Average</td>
                    <td>22.8</td>
                    <td>36.3</td>
                    <td>14.5</td>
                    <td>4.8</td>
                    <td>21.6</td>
                    <td>-</td>
                </tr>
                <tr>
                    <td>Count</td>
                    <td>4</td>
                    <td>4</td>
                    <td>4</td>
                    <td>4</td>
                    <td>4</td>
                    <td>-</td>
                </tr>
                <tr>
                    <td>Maximum</td>
                    <td>38.5</td>
                    <td>43.2</td>
                    <td>17.3</td>
                    <td>6.3</td>
                    <td>31.1</td>
                    <td>-</td>
                </tr>
                <tr>
                    <td>Minimum</td>
                    <td>12.0</td>
                    <td>29.3</td>
                    <td>22.2</td>
                    <td>14.3</td>
                    <td>21.3</td>
                    <td>-</td>
                </tr>
            </tbody>
        </table>
    </div>

    <script>
        // Data for the particle size distribution curve
        const particleSizes = [100, 20.0, 2.0, 0.5, 0.25, 0.075, 0.01];

        // Cumulative passing percentages (calculated from retained percentages)
        const cumulativeMean = [100, 77.2, 40.9, 26.4, 21.6, 0, 0];
        const cumulativeMax = [100, 87.5, 44.3, 27.0, 20.7, 0, 0];
        const cumulativeMin = [100, 61.5, 32.2, 10.0, 6.4, 0, 0];

        // Create the plot
        const trace1 = {
            x: particleSizes,
            y: cumulativeMean,
            mode: 'lines+markers',
            name: 'Average',
            line: {
                color: '#667eea',
                width: 4,
                shape: 'spline'
            },
            marker: {
                size: 10,
                color: 'white',
                line: {
                    color: '#667eea',
                    width: 3
                },
                symbol: 'circle'
            }
        };

        const trace2 = {
            x: particleSizes,
            y: cumulativeMax,
            mode: 'lines',
            name: 'Maximum',
            line: {
                color: '#ff6b6b',
                width: 3,
                dash: 'dash',
                shape: 'spline'
            }
        };

        const trace3 = {
            x: particleSizes,
            y: cumulativeMin,
            mode: 'lines',
            name: 'Minimum',
            line: {
                color: '#4ecdc4',
                width: 3,
                dash: 'dash',
                shape: 'spline'
            }
        };

        // Fill between max and min
        const traceFill = {
            x: particleSizes.concat(particleSizes.slice().reverse()),
            y: cumulativeMax.concat(cumulativeMin.slice().reverse()),
            fill: 'toself',
            fillcolor: 'rgba(102, 126, 234, 0.15)',
            line: {color: 'transparent'},
            name: 'Range',
            showlegend: true
        };

        const data = [traceFill, trace1, trace2, trace3];

        const layout = {
            title: {
                text: 'Particle Size Distribution Curve<br>Silty Sand',
                font: {
                    family: 'Arial',
                    size: 18,
                    color: '#333'
                }
            },
            xaxis: {
                title: {
                    text: 'Particle Size (mm)',
                    font: {
                        family: 'Arial',
                        size: 14,
                        color: '#333'
                    }
                },
                type: 'log',
                range: [Math.log10(0.01), Math.log10(100)],
                tickvals: [0.075, 0.15, 0.3, 0.6, 1.18, 2.36, 4.75, 9.5, 19, 37.5, 75],
                ticktext: ['0.075', '0.15', '0.30', '0.60', '1.18', '2.36', '4.75', '9.5', '19', '37.5', '75'],
                gridcolor: 'rgba(102, 126, 234, 0.2)',
                gridwidth: 1,
                zerolinecolor: 'rgba(102, 126, 234, 0.4)'
            },
            yaxis: {
                title: {
                    text: 'Percentage Passing (%)',
                    font: {
                        family: 'Arial',
                        size: 14,
                        color: '#333'
                    }
                },
                range: [0, 100],
                dtick: 10,
                gridcolor: 'rgba(102, 126, 234, 0.2)',
                gridwidth: 1,
                zerolinecolor: 'rgba(102, 126, 234, 0.4)'
            },
            plot_bgcolor: 'rgba(248, 249, 250, 0.8)',
            paper_bgcolor: 'rgba(255, 255, 255, 0.9)',
            font: {
                family: 'Arial',
                size: 12,
                color: '#333'
            },
            legend: {
                x: 0.7,
                y: 0.5,
                bgcolor: 'rgba(255,255,255,0.95)',
                bordercolor: 'rgba(102, 126, 234, 0.3)',
                borderwidth: 2,
                font: {
                    size: 12,
                    color: '#333'
                },
                orientation: 'v'
            },
            shapes: [
                // Vertical lines for soil classification
                {
                    type: 'line',
                    x0: 0.075, x1: 0.075,
                    y0: 0, y1: 100,
                    line: {
                        color: 'red',
                        width: 1,
                        dash: 'dot'
                    }
                },
                {
                    type: 'line',
                    x0: 2.0, x1: 2.0,
                    y0: 0, y1: 100,
                    line: {
                        color: 'red',
                        width: 1,
                        dash: 'dot'
                    }
                },
                {
                    type: 'line',
                    x0: 20.0, x1: 20.0,
                    y0: 0, y1: 100,
                    line: {
                        color: 'red',
                        width: 1,
                        dash: 'dot'
                    }
                }
            ],
            annotations: [
                {
                    x: Math.log10(0.03),
                    y: 95,
                    text: 'Fines<br>(<0.075mm)',
                    showarrow: false,
                    bgcolor: 'lightblue',
                    bordercolor: 'navy',
                    borderwidth: 1,
                    font: {size: 10}
                },
                {
                    x: Math.log10(0.4),
                    y: 95,
                    text: 'Sand<br>(0.075-2mm)',
                    showarrow: false,
                    bgcolor: 'lightyellow',
                    bordercolor: 'orange',
                    borderwidth: 1,
                    font: {size: 10}
                },
                {
                    x: Math.log10(6),
                    y: 95,
                    text: 'Gravel<br>(2-20mm)',
                    showarrow: false,
                    bgcolor: 'lightcoral',
                    bordercolor: 'darkred',
                    borderwidth: 1,
                    font: {size: 10}
                },
                {
                    x: Math.log10(40),
                    y: 95,
                    text: 'Cobbles<br>(>20mm)',
                    showarrow: false,
                    bgcolor: 'lightgreen',
                    bordercolor: 'darkgreen',
                    borderwidth: 1,
                    font: {size: 10}
                }
            ]
        };

        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToAdd: ['downloadPlot'],
            toImageButtonOptions: {
                format: 'png',
                filename: 'particle_size_distribution',
                height: 600,
                width: 1000,
                scale: 2
            }
        };

        Plotly.newPlot('chart', data, layout, config);
    </script>
</body>
</html>
