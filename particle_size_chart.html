<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Particle Size Distribution Curve</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .chart-container {
            width: 100%;
            height: 600px;
            margin: 20px 0;
        }
        .data-table {
            margin-top: 30px;
            width: 100%;
            border-collapse: collapse;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .data-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Particle Size Distribution Curve - Silty Sand</h1>
        
        <div id="chart" class="chart-container"></div>
        
        <table class="data-table">
            <thead>
                <tr>
                    <th rowspan="2">Soil Layer</th>
                    <th rowspan="2">Index Type</th>
                    <th colspan="6">Particle Size Composition (%)</th>
                </tr>
                <tr>
                    <th>&gt;20.0</th>
                    <th>20.0~2.00</th>
                    <th>2.00~0.50</th>
                    <th>0.50~0.25</th>
                    <th>0.25~0.075</th>
                    <th>&lt;0.075</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td rowspan="4">Silty Sand</td>
                    <td>Average</td>
                    <td>22.8</td>
                    <td>36.3</td>
                    <td>14.5</td>
                    <td>4.8</td>
                    <td>21.6</td>
                    <td>-</td>
                </tr>
                <tr>
                    <td>Count</td>
                    <td>4</td>
                    <td>4</td>
                    <td>4</td>
                    <td>4</td>
                    <td>4</td>
                    <td>-</td>
                </tr>
                <tr>
                    <td>Maximum</td>
                    <td>38.5</td>
                    <td>43.2</td>
                    <td>17.3</td>
                    <td>6.3</td>
                    <td>31.1</td>
                    <td>-</td>
                </tr>
                <tr>
                    <td>Minimum</td>
                    <td>12.0</td>
                    <td>29.3</td>
                    <td>22.2</td>
                    <td>14.3</td>
                    <td>21.3</td>
                    <td>-</td>
                </tr>
            </tbody>
        </table>
    </div>

    <script>
        // Data for the particle size distribution curve
        const particleSizes = [100, 20.0, 2.0, 0.5, 0.25, 0.075, 0.01];
        
        // Cumulative passing percentages (calculated from retained percentages)
        const cumulativeMean = [100, 77.2, 40.9, 26.4, 21.6, 0, 0];
        const cumulativeMax = [100, 87.5, 44.3, 27.0, 20.7, 0, 0];
        const cumulativeMin = [100, 61.5, 32.2, 10.0, 6.4, 0, 0];
        
        // Create the plot
        const trace1 = {
            x: particleSizes,
            y: cumulativeMean,
            mode: 'lines+markers',
            name: 'Average',
            line: {
                color: '#1f77b4',
                width: 3
            },
            marker: {
                size: 8,
                color: 'white',
                line: {
                    color: '#1f77b4',
                    width: 2
                }
            }
        };
        
        const trace2 = {
            x: particleSizes,
            y: cumulativeMax,
            mode: 'lines',
            name: 'Maximum',
            line: {
                color: '#d62728',
                width: 2,
                dash: 'dash'
            }
        };
        
        const trace3 = {
            x: particleSizes,
            y: cumulativeMin,
            mode: 'lines',
            name: 'Minimum',
            line: {
                color: '#2ca02c',
                width: 2,
                dash: 'dash'
            }
        };
        
        // Fill between max and min
        const traceFill = {
            x: particleSizes.concat(particleSizes.slice().reverse()),
            y: cumulativeMax.concat(cumulativeMin.slice().reverse()),
            fill: 'toself',
            fillcolor: 'rgba(255, 127, 14, 0.2)',
            line: {color: 'transparent'},
            name: 'Range',
            showlegend: true
        };
        
        const data = [traceFill, trace1, trace2, trace3];
        
        const layout = {
            title: {
                text: 'Particle Size Distribution Curve<br>Silty Sand',
                font: {
                    family: 'Arial',
                    size: 18,
                    color: '#333'
                }
            },
            xaxis: {
                title: {
                    text: 'Particle Size (mm)',
                    font: {
                        family: 'Arial',
                        size: 14,
                        color: '#333'
                    }
                },
                type: 'log',
                range: [Math.log10(0.01), Math.log10(100)],
                tickvals: [0.075, 0.15, 0.3, 0.6, 1.18, 2.36, 4.75, 9.5, 19, 37.5, 75],
                ticktext: ['0.075', '0.15', '0.30', '0.60', '1.18', '2.36', '4.75', '9.5', '19', '37.5', '75'],
                gridcolor: '#e6e6e6',
                gridwidth: 1
            },
            yaxis: {
                title: {
                    text: 'Percentage Passing (%)',
                    font: {
                        family: 'Arial',
                        size: 14,
                        color: '#333'
                    }
                },
                range: [0, 100],
                dtick: 10,
                gridcolor: '#e6e6e6',
                gridwidth: 1
            },
            plot_bgcolor: 'white',
            paper_bgcolor: 'white',
            font: {
                family: 'Arial',
                size: 12,
                color: '#333'
            },
            legend: {
                x: 0.7,
                y: 0.5,
                bgcolor: 'rgba(255,255,255,0.8)',
                bordercolor: '#333',
                borderwidth: 1
            },
            shapes: [
                // Vertical lines for soil classification
                {
                    type: 'line',
                    x0: 0.075, x1: 0.075,
                    y0: 0, y1: 100,
                    line: {
                        color: 'red',
                        width: 1,
                        dash: 'dot'
                    }
                },
                {
                    type: 'line',
                    x0: 2.0, x1: 2.0,
                    y0: 0, y1: 100,
                    line: {
                        color: 'red',
                        width: 1,
                        dash: 'dot'
                    }
                },
                {
                    type: 'line',
                    x0: 20.0, x1: 20.0,
                    y0: 0, y1: 100,
                    line: {
                        color: 'red',
                        width: 1,
                        dash: 'dot'
                    }
                }
            ],
            annotations: [
                {
                    x: Math.log10(0.03),
                    y: 95,
                    text: 'Fines<br>(<0.075mm)',
                    showarrow: false,
                    bgcolor: 'lightblue',
                    bordercolor: 'navy',
                    borderwidth: 1,
                    font: {size: 10}
                },
                {
                    x: Math.log10(0.4),
                    y: 95,
                    text: 'Sand<br>(0.075-2mm)',
                    showarrow: false,
                    bgcolor: 'lightyellow',
                    bordercolor: 'orange',
                    borderwidth: 1,
                    font: {size: 10}
                },
                {
                    x: Math.log10(6),
                    y: 95,
                    text: 'Gravel<br>(2-20mm)',
                    showarrow: false,
                    bgcolor: 'lightcoral',
                    bordercolor: 'darkred',
                    borderwidth: 1,
                    font: {size: 10}
                },
                {
                    x: Math.log10(40),
                    y: 95,
                    text: 'Cobbles<br>(>20mm)',
                    showarrow: false,
                    bgcolor: 'lightgreen',
                    bordercolor: 'darkgreen',
                    borderwidth: 1,
                    font: {size: 10}
                }
            ]
        };
        
        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToAdd: ['downloadPlot'],
            toImageButtonOptions: {
                format: 'png',
                filename: 'particle_size_distribution',
                height: 600,
                width: 1000,
                scale: 2
            }
        };
        
        Plotly.newPlot('chart', data, layout, config);
    </script>
</body>
</html>
