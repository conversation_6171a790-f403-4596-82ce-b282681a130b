import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import os

print("Creating simple test plot...")
print("Current directory:", os.getcwd())

try:
    # Simple test
    fig, ax = plt.subplots(figsize=(10, 6))
    x = [0.01, 0.075, 0.25, 0.5, 2.0, 20.0, 100]
    y = [0, 21.6, 26.4, 40.9, 77.2, 100, 100]

    ax.semilogx(x, y, 'o-', linewidth=2, markersize=6)
    ax.set_xlabel('Particle Size (mm)')
    ax.set_ylabel('Percentage Passing (%)')
    ax.set_title('Particle Size Distribution')
    ax.grid(True)

    print("Saving plot...")
    plt.savefig('test_plot.png', dpi=150, bbox_inches='tight')
    print("Test plot saved as test_plot.png")

    # Check if file was created
    if os.path.exists('test_plot.png'):
        print("File successfully created!")
        print("File size:", os.path.getsize('test_plot.png'), "bytes")
    else:
        print("File was not created!")

except Exception as e:
    print("Error occurred:", str(e))
    import traceback
    traceback.print_exc()
