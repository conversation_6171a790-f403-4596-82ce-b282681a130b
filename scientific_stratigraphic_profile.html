<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scientific Stratigraphic Profile</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #2c3e50;
        }
        h1 {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
            font-family: Arial, sans-serif;
        }
        .subtitle {
            font-size: 16px;
            color: #7f8c8d;
            font-style: italic;
            font-family: Arial, sans-serif;
        }
        .profile-container {
            width: 100%;
            height: 800px;
            margin: 30px 0;
            border: 2px solid #2c3e50;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .info-section {
            margin-top: 40px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        .layer-description {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 8px 25px rgba(44, 62, 80, 0.3);
        }
        .layer-description h3 {
            color: white;
            margin-top: 0;
            font-size: 18px;
            font-family: Arial, sans-serif;
        }
        .properties-table {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border: 1px solid #bdc3c7;
        }
        .properties-table h3 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 18px;
            font-family: Arial, sans-serif;
        }
        .property-item {
            margin: 12px 0;
            font-size: 14px;
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        .property-label {
            font-weight: bold;
            color: #2c3e50;
        }
        .property-value {
            color: #7f8c8d;
        }
        .legend {
            margin-top: 30px;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border: 1px solid #bdc3c7;
        }
        .legend h3 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 16px;
            font-family: Arial, sans-serif;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
            font-family: Arial, sans-serif;
            font-size: 14px;
        }
        .legend-symbol {
            width: 30px;
            height: 20px;
            margin-right: 15px;
            border: 1px solid #2c3e50;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #bdc3c7;
            font-size: 12px;
            color: #7f8c8d;
            text-align: center;
            font-family: Arial, sans-serif;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Scientific Stratigraphic Profile</h1>
            <div class="subtitle">Geotechnical Investigation - Layer 1 Analysis</div>
        </div>
        
        <div id="profile" class="profile-container"></div>
        
        <div class="info-section">
            <div class="layer-description">
                <h3>Layer 1: Silt (Saturated)</h3>
                <div class="property-item">
                    <span><strong>Soil Type:</strong> Silt</span>
                </div>
                <div class="property-item">
                    <span><strong>Saturation:</strong> Fully saturated</span>
                </div>
                <div class="property-item">
                    <span><strong>Uniformity:</strong> Non-uniform soil quality</span>
                </div>
                <div class="property-item">
                    <span><strong>Inclusions:</strong> Contains mica and scallop shells</span>
                </div>
                <div class="property-item">
                    <span><strong>Local Features:</strong> Contains fine sand locally</span>
                </div>
                <div class="property-item">
                    <span><strong>Shake Test:</strong> No dilatancy reaction</span>
                </div>
                <div class="property-item">
                    <span><strong>Dry Strength:</strong> Low</span>
                </div>
                <div class="property-item">
                    <span><strong>Toughness:</strong> Medium</span>
                </div>
            </div>
            
            <div class="properties-table">
                <h3>Engineering Properties</h3>
                <div class="property-item">
                    <span class="property-label">USCS Classification:</span>
                    <span class="property-value">ML (Low plasticity silt)</span>
                </div>
                <div class="property-item">
                    <span class="property-label">Plasticity Index:</span>
                    <span class="property-value">10-20</span>
                </div>
                <div class="property-item">
                    <span class="property-label">Liquid Limit:</span>
                    <span class="property-value">25-35%</span>
                </div>
                <div class="property-item">
                    <span class="property-label">Water Content:</span>
                    <span class="property-value">High (saturated)</span>
                </div>
                <div class="property-item">
                    <span class="property-label">Permeability:</span>
                    <span class="property-value">Low to medium</span>
                </div>
                <div class="property-item">
                    <span class="property-label">Compressibility:</span>
                    <span class="property-value">Medium to high</span>
                </div>
                <div class="property-item">
                    <span class="property-label">Bearing Capacity:</span>
                    <span class="property-value">Low to medium</span>
                </div>
                <div class="property-item">
                    <span class="property-label">Settlement Potential:</span>
                    <span class="property-value">Medium to high</span>
                </div>
            </div>
        </div>
        
        <div class="legend">
            <h3>Stratigraphic Legend</h3>
            <div class="legend-item">
                <div class="legend-symbol" style="background: linear-gradient(45deg, #8B7355 25%, transparent 25%), linear-gradient(-45deg, #8B7355 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #8B7355 75%), linear-gradient(-45deg, transparent 75%, #8B7355 75%); background-size: 4px 4px; background-color: #A0826D;"></div>
                <span>Silt - Non-uniform, saturated</span>
            </div>
            <div class="legend-item">
                <div class="legend-symbol" style="background: #2E8B57; position: relative;">
                    <div style="position: absolute; top: 2px; left: 2px; width: 4px; height: 4px; background: #FFD700; border-radius: 50%;"></div>
                    <div style="position: absolute; top: 8px; right: 3px; width: 3px; height: 3px; background: #FFD700; border-radius: 50%;"></div>
                </div>
                <span>Mica inclusions</span>
            </div>
            <div class="legend-item">
                <div class="legend-symbol" style="background: #A0826D; position: relative;">
                    <div style="position: absolute; top: 3px; left: 5px; width: 8px; height: 6px; background: #F5F5DC; border-radius: 50%; border: 1px solid #8B7355;"></div>
                    <div style="position: absolute; bottom: 2px; right: 4px; width: 6px; height: 5px; background: #F5F5DC; border-radius: 50%; border: 1px solid #8B7355;"></div>
                </div>
                <span>Scallop shells</span>
            </div>
            <div class="legend-item">
                <div class="legend-symbol" style="background: linear-gradient(to right, #A0826D 70%, #DEB887 70%);"></div>
                <span>Local fine sand content</span>
            </div>
            <div class="legend-item">
                <div class="legend-symbol" style="background: #4682B4; opacity: 0.7;"></div>
                <span>Groundwater level</span>
            </div>
        </div>
        
        <div class="footer">
            Scientific Stratigraphic Profile | Standards: ASTM D2488, ASTM D2487 | Font: Arial
        </div>
    </div>

    <script>
        // Create stratigraphic profile data
        function createStratigraphicProfile() {
            const traces = [];
            
            // Ground surface line
            traces.push({
                x: [0, 10],
                y: [0, 0],
                mode: 'lines',
                line: {color: '#2c3e50', width: 3},
                name: 'Ground Surface',
                hoverinfo: 'skip'
            });
            
            // Layer 1: Silt (0-5m depth)
            const layer1X = [0, 10, 10, 0, 0];
            const layer1Y = [0, 0, -5, -5, 0];
            
            traces.push({
                x: layer1X,
                y: layer1Y,
                fill: 'toself',
                fillcolor: 'rgba(160, 130, 109, 0.8)',
                line: {color: '#8B7355', width: 2},
                name: 'Layer 1: Silt (Saturated)',
                hovertemplate: '<b>Layer 1: Silt</b><br>Depth: 0-5m<br>Saturated, non-uniform<br>Contains mica & scallop shells<extra></extra>'
            });
            
            // Add mica particles (small golden dots)
            const micaX = [1, 2.5, 4, 5.5, 7, 8.5, 1.5, 3.5, 6, 8, 2, 4.5, 7.5, 9];
            const micaY = [-0.5, -1.2, -0.8, -2.1, -1.5, -0.7, -2.8, -1.8, -2.5, -3.2, -3.8, -4.1, -3.5, -4.3];
            
            traces.push({
                x: micaX,
                y: micaY,
                mode: 'markers',
                marker: {
                    size: 6,
                    color: '#FFD700',
                    symbol: 'diamond',
                    line: {color: '#B8860B', width: 1}
                },
                name: 'Mica Inclusions',
                hovertemplate: '<b>Mica</b><br>Mineral inclusion<extra></extra>'
            });
            
            // Add scallop shells (larger oval shapes)
            const shellX = [1.8, 4.2, 6.8, 3.1, 7.2, 2.3, 5.7, 8.7];
            const shellY = [-1.3, -2.4, -1.8, -3.2, -3.8, -4.2, -2.1, -2.9];
            
            traces.push({
                x: shellX,
                y: shellY,
                mode: 'markers',
                marker: {
                    size: 12,
                    color: '#F5F5DC',
                    symbol: 'square',
                    line: {color: '#8B7355', width: 2}
                },
                name: 'Scallop Shells',
                hovertemplate: '<b>Scallop Shell</b><br>Organic inclusion<extra></extra>'
            });
            
            // Add fine sand pockets (lighter colored areas)
            traces.push({
                x: [7.5, 9.5, 9.5, 7.5, 7.5],
                y: [-1.5, -1.5, -2.5, -2.5, -1.5],
                fill: 'toself',
                fillcolor: 'rgba(222, 184, 135, 0.6)',
                line: {color: '#DEB887', width: 1, dash: 'dot'},
                name: 'Local Fine Sand',
                hovertemplate: '<b>Fine Sand Pocket</b><br>Local inclusion<extra></extra>'
            });
            
            traces.push({
                x: [2, 4, 4, 2, 2],
                y: [-3.5, -3.5, -4.5, -4.5, -3.5],
                fill: 'toself',
                fillcolor: 'rgba(222, 184, 135, 0.6)',
                line: {color: '#DEB887', width: 1, dash: 'dot'},
                name: 'Local Fine Sand',
                showlegend: false,
                hovertemplate: '<b>Fine Sand Pocket</b><br>Local inclusion<extra></extra>'
            });
            
            // Groundwater level indicator
            traces.push({
                x: [0, 10],
                y: [-0.5, -0.5],
                mode: 'lines',
                line: {color: '#4682B4', width: 3, dash: 'dash'},
                name: 'Groundwater Level',
                hovertemplate: '<b>Groundwater Level</b><br>Depth: 0.5m<extra></extra>'
            });
            
            // Depth markers
            const depths = [0, -1, -2, -3, -4, -5];
            const depthLabels = ['0m', '1m', '2m', '3m', '4m', '5m'];
            
            traces.push({
                x: Array(depths.length).fill(-0.5),
                y: depths,
                mode: 'markers+text',
                marker: {size: 8, color: '#2c3e50'},
                text: depthLabels,
                textposition: 'middle left',
                textfont: {size: 12, color: '#2c3e50', family: 'Arial'},
                name: 'Depth Markers',
                hoverinfo: 'skip'
            });
            
            // Add soil description annotations
            const annotations = [
                {
                    x: 5,
                    y: -1,
                    text: 'SILT (ML)<br>Saturated, Non-uniform<br>Contains mica & scallop shells',
                    showarrow: true,
                    arrowhead: 2,
                    arrowsize: 1,
                    arrowwidth: 2,
                    arrowcolor: '#2c3e50',
                    font: {size: 12, color: '#2c3e50', family: 'Arial'},
                    bgcolor: 'rgba(255,255,255,0.9)',
                    bordercolor: '#2c3e50',
                    borderwidth: 2
                },
                {
                    x: 8.5,
                    y: -3.5,
                    text: 'Properties:<br>• No shake reaction<br>• Low dry strength<br>• Medium toughness',
                    showarrow: true,
                    arrowhead: 2,
                    arrowsize: 1,
                    arrowwidth: 2,
                    arrowcolor: '#2c3e50',
                    font: {size: 10, color: '#2c3e50', family: 'Arial'},
                    bgcolor: 'rgba(255,255,255,0.9)',
                    bordercolor: '#2c3e50',
                    borderwidth: 1
                }
            ];
            
            return {traces, annotations};
        }
        
        const {traces, annotations} = createStratigraphicProfile();
        
        const layout = {
            title: {
                text: 'Stratigraphic Profile - Layer 1',
                font: {
                    family: 'Arial',
                    size: 18,
                    color: '#2c3e50'
                }
            },
            xaxis: {
                title: {
                    text: 'Horizontal Distance (m)',
                    font: {
                        family: 'Arial',
                        size: 14,
                        color: '#2c3e50'
                    }
                },
                range: [-1, 11],
                showgrid: true,
                gridcolor: '#ecf0f1',
                tickfont: {
                    family: 'Arial',
                    size: 12,
                    color: '#2c3e50'
                }
            },
            yaxis: {
                title: {
                    text: 'Elevation (m)',
                    font: {
                        family: 'Arial',
                        size: 14,
                        color: '#2c3e50'
                    }
                },
                range: [-6, 1],
                showgrid: true,
                gridcolor: '#ecf0f1',
                tickfont: {
                    family: 'Arial',
                    size: 12,
                    color: '#2c3e50'
                }
            },
            plot_bgcolor: 'white',
            paper_bgcolor: 'rgba(255,255,255,0.9)',
            font: {
                family: 'Arial',
                size: 12,
                color: '#2c3e50'
            },
            legend: {
                x: 0.02,
                y: 0.98,
                bgcolor: 'rgba(255,255,255,0.9)',
                bordercolor: '#bdc3c7',
                borderwidth: 1,
                font: {
                    family: 'Arial',
                    size: 11,
                    color: '#2c3e50'
                }
            },
            annotations: annotations,
            hovermode: 'closest'
        };
        
        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToAdd: ['downloadPlot'],
            toImageButtonOptions: {
                format: 'png',
                filename: 'stratigraphic_profile_layer1',
                height: 800,
                width: 1000,
                scale: 2
            }
        };
        
        Plotly.newPlot('profile', traces, layout, config);
    </script>
</body>
</html>
